# نموذج تسجيل الدخول باستخدام DevExpress

## الوصف
تم إنشاء نموذج تسجيل دخول جديد باستخدام مكتبة DevExpress v20.1 مع تصميم عصري ودعم اللغة العربية.

## الميزات
- تصميم عصري باستخدام DevExpress XtraForm
- دعم كامل للغة العربية (RTL)
- استخدام LayoutControl لتنسيق العناصر
- ثيم Office 2016 Colorful
- أبعاد محددة: 500x650 بكسل
- رسائل خطأ باللغة العربية
- دعم التنقل بمفتاح Enter

## المتطلبات
- Visual Studio 2013 أو أحدث
- DevExpress v20.1
- .NET Framework 4.0 أو أحدث

## الملفات المضافة
- `Forms/DevExpressLoginForm.vb` - النموذج الرئيسي
- `TestDevExpressLoginForm.bat` - ملف تشغيل للاختبار

## كيفية الاستخدام

### 1. التشغيل من Visual Studio
1. افتح المشروع في Visual Studio
2. تأكد من أن DevExpress v20.1 مثبت
3. اضغط F5 لتشغيل المشروع

### 2. التشغيل من ملف Batch
```batch
TestDevExpressLoginForm.bat
```

### 3. الاستخدام البرمجي
```vb
Dim loginForm As New DevExpressLoginForm()
If loginForm.ShowDialog() = DialogResult.OK Then
    ' تم تسجيل الدخول بنجاح
    MessageBox.Show("مرحباً بك!")
End If
```

## بيانات الدخول الافتراضية
- اسم المستخدم: `admin`
- كلمة المرور: `admin`

## التخصيص
يمكن تخصيص النموذج من خلال:
- تغيير الألوان في `SetupControlAppearance()`
- تعديل النصوص في `SetupControlProperties()`
- إضافة المزيد من التحقق في `BtnLogin_Click()`

## الملاحظات
- النموذج يستخدم DevExpress LayoutControl للتنسيق التلقائي
- جميع النصوص باللغة العربية
- يدعم التنقل بمفتاح Enter بين الحقول
- يتم توسيط النموذج تلقائياً على الشاشة

## استكشاف الأخطاء
إذا واجهت مشاكل:
1. تأكد من تثبيت DevExpress v20.1
2. تحقق من المراجع في المشروع
3. تأكد من وجود الملفات المطلوبة في مجلد bin
