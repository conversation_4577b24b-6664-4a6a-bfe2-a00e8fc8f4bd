Imports System.Data.SQLite
Imports System.IO

''' <summary>
''' فئة بسيطة لإدارة قاعدة البيانات SQLite
''' </summary>
Public Class SimpleDatabaseConnection
    Private Shared _connectionString As String = ""
    Private Shared _databasePath As String = ""

    ''' <summary>
    ''' الحصول على مسار قاعدة البيانات
    ''' </summary>
    Public Shared ReadOnly Property DatabasePath As String
        Get
            If String.IsNullOrEmpty(_databasePath) Then
                Dim appDataPath As String = Path.Combine(Application.StartupPath, "App_Data")
                If Not Directory.Exists(appDataPath) Then
                    Directory.CreateDirectory(appDataPath)
                End If
                _databasePath = Path.Combine(appDataPath, "UnifiedAccountingDB.db")
            End If
            Return _databasePath
        End Get
    End Property

    ''' <summary>
    ''' الحصول على نص الاتصال
    ''' </summary>
    Public Shared ReadOnly Property ConnectionString As String
        Get
            If String.IsNullOrEmpty(_connectionString) Then
                _connectionString = $"Data Source={DatabasePath};Version=3;UTF8Encoding=True;"
            End If
            Return _connectionString
        End Get
    End Property

    ''' <summary>
    ''' إنشاء اتصال جديد
    ''' </summary>
    Public Shared Function CreateConnection() As SQLiteConnection
        Return New SQLiteConnection(ConnectionString)
    End Function

    ''' <summary>
    ''' اختبار الاتصال
    ''' </summary>
    Public Shared Function TestConnection() As Boolean
        Try
            Using conn As SQLiteConnection = CreateConnection()
                conn.Open()
                Return True
            End Using
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' إنشاء قاعدة البيانات إذا لم تكن موجودة
    ''' </summary>
    Public Shared Function CreateDatabaseIfNotExists() As Boolean
        Try
            If Not File.Exists(DatabasePath) Then
                SQLiteConnection.CreateFile(DatabasePath)
                CreateTables()
                InsertInitialData()
            End If
            Return True
        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء قاعدة البيانات: " & ex.Message)
        End Try
    End Function

    ''' <summary>
    ''' إنشاء الجداول
    ''' </summary>
    Private Shared Sub CreateTables()
        Dim sql As String = "
        CREATE TABLE IF NOT EXISTS Ministries (
            MinistryID INTEGER PRIMARY KEY AUTOINCREMENT,
            MinistryName TEXT NOT NULL,
            MinistryCode TEXT NOT NULL UNIQUE,
            IsActive INTEGER DEFAULT 1,
            CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS Departments (
            DepartmentID INTEGER PRIMARY KEY AUTOINCREMENT,
            DepartmentName TEXT NOT NULL,
            DepartmentCode TEXT NOT NULL,
            MinistryID INTEGER NOT NULL,
            IsActive INTEGER DEFAULT 1,
            CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (MinistryID) REFERENCES Ministries(MinistryID)
        );

        CREATE TABLE IF NOT EXISTS Users (
            UserID INTEGER PRIMARY KEY AUTOINCREMENT,
            Username TEXT NOT NULL UNIQUE,
            PasswordHash TEXT NOT NULL,
            FullName TEXT NOT NULL,
            Role TEXT NOT NULL,
            IsActive INTEGER DEFAULT 1,
            CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP
        );
        "

        Using conn As SQLiteConnection = CreateConnection()
            conn.Open()
            Using cmd As New SQLiteCommand(sql, conn)
                cmd.ExecuteNonQuery()
            End Using
        End Using
    End Sub

    ''' <summary>
    ''' إدراج البيانات الأولية
    ''' </summary>
    Private Shared Sub InsertInitialData()
        Dim sql As String = "
        INSERT OR IGNORE INTO Ministries (MinistryName, MinistryCode) VALUES ('وزارة الشباب والرياضة', 'MYS');
        INSERT OR IGNORE INTO Departments (DepartmentName, DepartmentCode, MinistryID) VALUES ('دائرة الطب الرياضي', 'SMD', 1);
        INSERT OR IGNORE INTO Users (Username, PasswordHash, FullName, Role) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');
        "

        Using conn As SQLiteConnection = CreateConnection()
            conn.Open()
            Using cmd As New SQLiteCommand(sql, conn)
                cmd.ExecuteNonQuery()
            End Using
        End Using
    End Sub

    ''' <summary>
    ''' تنفيذ استعلام وإرجاع النتيجة
    ''' </summary>
    Public Shared Function ExecuteQuery(sql As String) As DataTable
        Dim dt As New DataTable()
        Using conn As SQLiteConnection = CreateConnection()
            conn.Open()
            Using cmd As New SQLiteCommand(sql, conn)
                Using adapter As New SQLiteDataAdapter(cmd)
                    adapter.Fill(dt)
                End Using
            End Using
        End Using
        Return dt
    End Function

    ''' <summary>
    ''' تنفيذ أمر بدون إرجاع نتيجة
    ''' </summary>
    Public Shared Function ExecuteNonQuery(sql As String) As Integer
        Using conn As SQLiteConnection = CreateConnection()
            conn.Open()
            Using cmd As New SQLiteCommand(sql, conn)
                Return cmd.ExecuteNonQuery()
            End Using
        End Using
    End Function
End Class
