Imports System.Windows.Forms
Imports System.Drawing

''' <summary>
''' اختبار ظهور عناصر شاشة تسجيل الدخول
''' </summary>
Module TestLoginFormVisibility
    
    ''' <summary>
    ''' اختبار شامل لشاشة تسجيل الدخول
    ''' </summary>
    Sub TestLoginFormElements()
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)
        
        Console.WriteLine("=== اختبار شاشة تسجيل الدخول ===")
        Console.WriteLine()
        
        Try
            Dim loginForm As New LoginForm()
            
            ' اختبار الخصائص الأساسية
            Console.WriteLine("✓ تم إنشاء الشاشة بنجاح")
            Console.WriteLine($"الحجم: {loginForm.Size.Width} x {loginForm.Size.Height}")
            Console.WriteLine($"الموضع: {loginForm.Location.X}, {loginForm.Location.Y}")
            Console.WriteLine()
            
            ' اختبار العناصر
            TestFormControls(loginForm)
            
            ' عرض الشاشة
            Console.WriteLine("🚀 عرض الشاشة...")
            Application.Run(loginForm)
            
        Catch ex As Exception
            Console.WriteLine($"❌ خطأ: {ex.Message}")
            Console.WriteLine($"التفاصيل: {ex.StackTrace}")
        End Try
    End Sub
    
    ''' <summary>
    ''' اختبار عناصر الشاشة
    ''' </summary>
    Private Sub TestFormControls(form As LoginForm)
        Console.WriteLine("=== اختبار العناصر ===")
        
        ' البحث عن العناصر
        Dim pnlMain As Panel = FindControl(Of Panel)(form, "pnlMain")
        Dim pnlHeader As Panel = FindControl(Of Panel)(form, "pnlHeader")
        Dim pnlLogin As Panel = FindControl(Of Panel)(form, "pnlLogin")
        Dim lblTitle As Label = FindControl(Of Label)(form, "lblTitle")
        Dim lblSubtitle As Label = FindControl(Of Label)(form, "lblSubtitle")
        Dim lblUsername As Label = FindControl(Of Label)(form, "lblUsername")
        Dim lblPassword As Label = FindControl(Of Label)(form, "lblPassword")
        Dim txtUsername As TextBox = FindControl(Of TextBox)(form, "txtUsername")
        Dim txtPassword As TextBox = FindControl(Of TextBox)(form, "txtPassword")
        Dim btnLogin As Button = FindControl(Of Button)(form, "btnLogin")
        Dim btnCancel As Button = FindControl(Of Button)(form, "btnCancel")
        
        ' فحص العناصر
        CheckControl("pnlMain", pnlMain)
        CheckControl("pnlHeader", pnlHeader)
        CheckControl("pnlLogin", pnlLogin)
        CheckControl("lblTitle", lblTitle)
        CheckControl("lblSubtitle", lblSubtitle)
        CheckControl("lblUsername", lblUsername)
        CheckControl("lblPassword", lblPassword)
        CheckControl("txtUsername", txtUsername)
        CheckControl("txtPassword", txtPassword)
        CheckControl("btnLogin", btnLogin)
        CheckControl("btnCancel", btnCancel)
        
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' البحث عن عنصر تحكم
    ''' </summary>
    Private Function FindControl(Of T As Control)(parent As Control, name As String) As T
        For Each ctrl As Control In parent.Controls
            If ctrl.Name = name AndAlso TypeOf ctrl Is T Then
                Return DirectCast(ctrl, T)
            End If
            
            If ctrl.HasChildren Then
                Dim found As T = FindControl(Of T)(ctrl, name)
                If found IsNot Nothing Then
                    Return found
                End If
            End If
        Next
        
        Return Nothing
    End Function
    
    ''' <summary>
    ''' فحص عنصر تحكم
    ''' </summary>
    Private Sub CheckControl(name As String, ctrl As Control)
        If ctrl IsNot Nothing Then
            Console.WriteLine($"✓ {name}: موجود - الموضع ({ctrl.Location.X}, {ctrl.Location.Y}) - الحجم ({ctrl.Size.Width} x {ctrl.Size.Height}) - مرئي: {ctrl.Visible}")
        Else
            Console.WriteLine($"❌ {name}: غير موجود!")
        End If
    End Sub
    
End Module
