Imports System
Imports System.Drawing
Imports System.Windows.Forms
Imports DevExpress.XtraEditors
Imports DevExpress.Utils
Imports DevExpress.XtraLayout
Imports DevExpress.XtraLayout.Utils
Imports DevExpress.LookAndFeel

Namespace UnifiedAccountingSystem
    ''' <summary>
    ''' شاشة تسجيل الدخول باستخدام DevExpress
    ''' </summary>
    Public Class DevExpressLoginForm
        Inherits XtraForm

#Region "Controls"
        Private layoutControl As LayoutControl
        Private layoutControlGroup As LayoutControlGroup
        Private txtUsername As TextEdit
        Private txtPassword As TextEdit
        Private btnLogin As SimpleButton
        Private btnCancel As SimpleButton
        Private lblTitle As LabelControl
        Private lblSubtitle As LabelControl
        Private layoutItemUsername As LayoutControlItem
        Private layoutItemPassword As LayoutControlItem
        Private layoutItemLogin As LayoutControlItem
        Private layoutItemCancel As LayoutControlItem
        Private layoutItemTitle As LayoutControlItem
        Private layoutItemSubtitle As LayoutControlItem
#End Region

#Region "Constructor"
        Public Sub New()
            InitializeComponent()
            SetupForm()
            SetupLayout()
            SetupEvents()
            CenterFormOnScreen()
        End Sub
#End Region

#Region "Form Setup"
        Private Sub SetupForm()
            ' إعدادات الشاشة الأساسية
            Me.Text = "تسجيل الدخول - النظام المحاسبي الموحد"
            Me.Size = New Size(500, 650)
            Me.FormBorderStyle = FormBorderStyle.FixedDialog
            Me.MaximizeBox = False
            Me.MinimizeBox = False
            Me.StartPosition = FormStartPosition.Manual
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            ' تطبيق ثيم DevExpress متوافق مع v20.1
            DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle("Office 2016 Colorful")
        End Sub

        Private Sub SetupLayout()
            ' إنشاء LayoutControl الرئيسي
            layoutControl = New LayoutControl()
            layoutControl.Dock = DockStyle.Fill
            Me.Controls.Add(layoutControl)

            ' إنشاء LayoutControlGroup
            layoutControlGroup = New LayoutControlGroup()
            layoutControlGroup.EnableIndentsWithoutBorders = DefaultBoolean.True
            layoutControlGroup.GroupBordersVisible = False
            layoutControlGroup.Text = ""
            layoutControl.Root = layoutControlGroup

            ' إنشاء العناصر
            CreateControls()
            CreateLayoutItems()
            SetupControlProperties()
        End Sub

        Private Sub CreateControls()
            ' عنوان النظام
            lblTitle = New LabelControl()
            lblTitle.Text = "النظام المحاسبي الموحد"
            lblTitle.Appearance.Font = New Font("Tahoma", 16, FontStyle.Bold)
            lblTitle.Appearance.ForeColor = Color.FromArgb(52, 152, 219)
            lblTitle.Appearance.TextOptions.HAlignment = HorzAlignment.Center

            ' العنوان الفرعي
            lblSubtitle = New LabelControl()
            lblSubtitle.Text = "وزارة الشباب والرياضة"
            lblSubtitle.Appearance.Font = New Font("Tahoma", 12)
            lblSubtitle.Appearance.ForeColor = Color.FromArgb(127, 140, 141)
            lblSubtitle.Appearance.TextOptions.HAlignment = HorzAlignment.Center

            ' حقل اسم المستخدم
            txtUsername = New TextEdit()
            txtUsername.Properties.NullText = "أدخل اسم المستخدم"
            txtUsername.Properties.Appearance.Font = New Font("Tahoma", 12)
            txtUsername.Size = New Size(300, 30)

            ' حقل كلمة المرور
            txtPassword = New TextEdit()
            txtPassword.Properties.NullText = "أدخل كلمة المرور"
            txtPassword.Properties.PasswordChar = "*"c
            txtPassword.Properties.Appearance.Font = New Font("Tahoma", 12)
            txtPassword.Size = New Size(300, 30)

            ' زر الدخول
            btnLogin = New SimpleButton()
            btnLogin.Text = "دخول"
            btnLogin.Appearance.Font = New Font("Tahoma", 12, FontStyle.Bold)
            btnLogin.Appearance.BackColor = Color.FromArgb(52, 152, 219)
            btnLogin.Appearance.ForeColor = Color.White
            btnLogin.Size = New Size(120, 40)
            btnLogin.LookAndFeel.UseDefaultLookAndFeel = False
            btnLogin.LookAndFeel.SkinName = "Office 2016 Colorful"

            ' زر الإلغاء
            btnCancel = New SimpleButton()
            btnCancel.Text = "إلغاء"
            btnCancel.Appearance.Font = New Font("Tahoma", 12, FontStyle.Bold)
            btnCancel.Appearance.BackColor = Color.FromArgb(231, 76, 60)
            btnCancel.Appearance.ForeColor = Color.White
            btnCancel.Size = New Size(120, 40)
            btnCancel.LookAndFeel.UseDefaultLookAndFeel = False
            btnCancel.LookAndFeel.SkinName = "Office 2016 Colorful"
        End Sub

        Private Sub CreateLayoutItems()
            ' إضافة العناصر إلى Layout
            layoutItemTitle = New LayoutControlItem(layoutControl, lblTitle)
            layoutItemTitle.Text = ""
            layoutItemTitle.TextVisible = False
            layoutItemTitle.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
            layoutItemTitle.MaxSize = New Size(0, 50)
            layoutItemTitle.MinSize = New Size(0, 50)

            layoutItemSubtitle = New LayoutControlItem(layoutControl, lblSubtitle)
            layoutItemSubtitle.Text = ""
            layoutItemSubtitle.TextVisible = False
            layoutItemSubtitle.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
            layoutItemSubtitle.MaxSize = New Size(0, 30)
            layoutItemSubtitle.MinSize = New Size(0, 30)

            layoutItemUsername = New LayoutControlItem(layoutControl, txtUsername)
            layoutItemUsername.Text = "اسم المستخدم:"
            layoutItemUsername.TextLocation = DevExpress.Utils.Locations.Top

            layoutItemPassword = New LayoutControlItem(layoutControl, txtPassword)
            layoutItemPassword.Text = "كلمة المرور:"
            layoutItemPassword.TextLocation = DevExpress.Utils.Locations.Top

            ' إنشاء مجموعة للأزرار
            Dim buttonGroup As New LayoutControlGroup()
            buttonGroup.LayoutMode = DevExpress.XtraLayout.LayoutMode.Table
            buttonGroup.OptionsTableLayoutGroup.ColumnDefinitions.Add(New DevExpress.XtraLayout.ColumnDefinition(DevExpress.XtraLayout.SizeType.Percent, 50))
            buttonGroup.OptionsTableLayoutGroup.ColumnDefinitions.Add(New DevExpress.XtraLayout.ColumnDefinition(DevExpress.XtraLayout.SizeType.Percent, 50))
            buttonGroup.GroupBordersVisible = False
            buttonGroup.Text = ""

            layoutItemCancel = New LayoutControlItem(layoutControl, btnCancel)
            layoutItemCancel.Text = ""
            layoutItemCancel.TextVisible = False
            layoutItemCancel.OptionsTableLayoutItem.ColumnIndex = 0
            buttonGroup.AddItem(layoutItemCancel)

            layoutItemLogin = New LayoutControlItem(layoutControl, btnLogin)
            layoutItemLogin.Text = ""
            layoutItemLogin.TextVisible = False
            layoutItemLogin.OptionsTableLayoutItem.ColumnIndex = 1
            buttonGroup.AddItem(layoutItemLogin)

            ' إضافة مجموعة الأزرار إلى المجموعة الرئيسية
            layoutControlGroup.AddItem(buttonGroup)
        End Sub

        Private Sub SetupControlProperties()
            ' تخصيص خصائص Layout
            layoutControlGroup.Padding = New DevExpress.XtraLayout.Utils.Padding(40, 40, 40, 40)
            layoutItemUsername.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 10, 10)
            layoutItemPassword.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 10, 20)
            layoutItemTitle.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 20, 10)
            layoutItemSubtitle.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 10, 30)
        End Sub
#End Region

#Region "Events"
        Private Sub SetupEvents()
            AddHandler btnLogin.Click, AddressOf BtnLogin_Click
            AddHandler btnCancel.Click, AddressOf BtnCancel_Click
            AddHandler txtUsername.KeyPress, AddressOf TextBox_KeyPress
            AddHandler txtPassword.KeyPress, AddressOf TextBox_KeyPress
        End Sub

        Private Sub BtnLogin_Click(sender As Object, e As EventArgs)
            Try
                Dim username As String = txtUsername.Text.Trim()
                Dim password As String = txtPassword.Text.Trim()

                If String.IsNullOrEmpty(username) Then
                    XtraMessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    txtUsername.Focus()
                    Return
                End If

                If String.IsNullOrEmpty(password) Then
                    XtraMessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    txtPassword.Focus()
                    Return
                End If

                ' التحقق من بيانات الدخول (مؤقت)
                If username = "admin" AndAlso password = "admin" Then
                    XtraMessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Me.DialogResult = DialogResult.OK
                    Me.Close()
                Else
                    XtraMessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    txtPassword.Text = ""
                    txtUsername.Focus()
                End If

            Catch ex As Exception
            XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
            Me.DialogResult = DialogResult.Cancel
            Me.Close()
        End Sub

        Private Sub TextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
            If e.KeyChar = Convert.ToChar(Keys.Enter) Then
                If sender Is txtUsername Then
                    txtPassword.Focus()
                ElseIf sender Is txtPassword Then
                    BtnLogin_Click(sender, e)
                End If
            End If
        End Sub
#End Region

#Region "Helper Methods"
        Private Sub CenterFormOnScreen()
            Dim workingArea As Rectangle = Screen.PrimaryScreen.WorkingArea
            Dim x As Integer = (workingArea.Width - Me.Width) \ 2
            Dim y As Integer = (workingArea.Height - Me.Height) \ 2
            Me.Location = New Point(x, y)
        End Sub

        Private Sub InitializeComponent()
            Me.SuspendLayout()
            Me.AutoScaleDimensions = New SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = AutoScaleMode.Font
            Me.ClientSize = New Size(500, 650)
            Me.Name = "DevExpressLoginForm"
            Me.ResumeLayout(False)
        End Sub
#End Region

    End Class
End Namespace
