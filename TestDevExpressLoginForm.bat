@echo off
echo Testing DevExpress Login Form...
echo.

REM Check if DevExpress is available
echo Checking DevExpress installation...
if exist "C:\Program Files (x86)\DevExpress 20.1\Components\Bin\Framework\DevExpress.Data.v20.1.dll" (
    echo DevExpress 20.1 found!
) else (
    echo Warning: DevExpress 20.1 not found in default location
)

echo.
echo Building and running the application...
echo.

REM Try to build and run using Visual Studio
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio 12.0\Common7\IDE\devenv.exe" (
    echo Using Visual Studio 2013...
    "%ProgramFiles(x86)%\Microsoft Visual Studio 12.0\Common7\IDE\devenv.exe" UnifiedAccountingSystem.sln /build Debug
    if exist "bin\Debug\UnifiedAccountingSystem.exe" (
        echo Running application...
        start "" "bin\Debug\UnifiedAccountingSystem.exe"
    ) else (
        echo Build failed or executable not found
    )
) else (
    echo Visual Studio 2013 not found
    echo Please build the project manually in Visual Studio
)

echo.
echo Press any key to continue...
pause > nul
