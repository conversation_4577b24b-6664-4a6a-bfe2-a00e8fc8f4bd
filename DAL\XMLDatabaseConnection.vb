Imports System.IO
Imports System.Xml

''' <summary>
''' فئة بسيطة لإدارة البيانات باستخدام XML (بدون قاعدة بيانات خارجية)
''' </summary>
Public Class XMLDatabaseConnection
    Private Shared _dataPath As String = ""
    Private Shared _usersFile As String = ""

    ''' <summary>
    ''' الحصول على مسار البيانات
    ''' </summary>
    Public Shared ReadOnly Property DataPath As String
        Get
            If String.IsNullOrEmpty(_dataPath) Then
                _dataPath = Path.Combine(Application.StartupPath, "Data")
                If Not Directory.Exists(_dataPath) Then
                    Directory.CreateDirectory(_dataPath)
                End If
            End If
            Return _dataPath
        End Get
    End Property

    ''' <summary>
    ''' مسار ملف المستخدمين
    ''' </summary>
    Public Shared ReadOnly Property UsersFile As String
        Get
            If String.IsNullOrEmpty(_usersFile) Then
                _usersFile = Path.Combine(DataPath, "Users.xml")
            End If
            Return _usersFile
        End Get
    End Property

    ''' <summary>
    ''' إنشاء ملفات البيانات إذا لم تكن موجودة
    ''' </summary>
    Public Shared Function CreateDataFilesIfNotExists() As Boolean
        Try
            If Not File.Exists(UsersFile) Then
                CreateUsersFile()
            End If
            Return True
        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء ملفات البيانات: " & ex.Message)
        End Try
    End Function

    ''' <summary>
    ''' إنشاء ملف المستخدمين
    ''' </summary>
    Private Shared Sub CreateUsersFile()
        Dim xmlDoc As New XmlDocument()
        
        ' إنشاء العقدة الجذر
        Dim root As XmlElement = xmlDoc.CreateElement("Users")
        xmlDoc.AppendChild(root)
        
        ' إضافة مستخدم افتراضي
        Dim userElement As XmlElement = xmlDoc.CreateElement("User")
        
        Dim userIdElement As XmlElement = xmlDoc.CreateElement("UserID")
        userIdElement.InnerText = "1"
        userElement.AppendChild(userIdElement)
        
        Dim usernameElement As XmlElement = xmlDoc.CreateElement("Username")
        usernameElement.InnerText = "admin"
        userElement.AppendChild(usernameElement)
        
        Dim passwordElement As XmlElement = xmlDoc.CreateElement("PasswordHash")
        passwordElement.InnerText = "admin123"
        userElement.AppendChild(passwordElement)
        
        Dim fullNameElement As XmlElement = xmlDoc.CreateElement("FullName")
        fullNameElement.InnerText = "مدير النظام"
        userElement.AppendChild(fullNameElement)
        
        Dim roleElement As XmlElement = xmlDoc.CreateElement("Role")
        roleElement.InnerText = "مدير"
        userElement.AppendChild(roleElement)
        
        Dim isActiveElement As XmlElement = xmlDoc.CreateElement("IsActive")
        isActiveElement.InnerText = "1"
        userElement.AppendChild(isActiveElement)
        
        Dim createdDateElement As XmlElement = xmlDoc.CreateElement("CreatedDate")
        createdDateElement.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        userElement.AppendChild(createdDateElement)
        
        root.AppendChild(userElement)
        
        ' حفظ الملف
        xmlDoc.Save(UsersFile)
    End Sub

    ''' <summary>
    ''' التحقق من صحة بيانات المستخدم
    ''' </summary>
    Public Shared Function AuthenticateUser(username As String, password As String) As XmlNode
        Try
            If Not File.Exists(UsersFile) Then
                CreateDataFilesIfNotExists()
            End If
            
            Dim xmlDoc As New XmlDocument()
            xmlDoc.Load(UsersFile)
            
            ' البحث عن المستخدم
            Dim xpath As String = $"//User[Username='{username}' and PasswordHash='{password}' and IsActive='1']"
            Dim userNode As XmlNode = xmlDoc.SelectSingleNode(xpath)
            
            Return userNode
            
        Catch ex As Exception
            Throw New Exception("خطأ في التحقق من بيانات المستخدم: " & ex.Message)
        End Try
    End Function

    ''' <summary>
    ''' الحصول على قيمة عقدة فرعية
    ''' </summary>
    Public Shared Function GetNodeValue(parentNode As XmlNode, nodeName As String) As String
        Dim childNode As XmlNode = parentNode.SelectSingleNode(nodeName)
        If childNode IsNot Nothing Then
            Return childNode.InnerText
        Else
            Return ""
        End If
    End Function

    ''' <summary>
    ''' اختبار النظام
    ''' </summary>
    Public Shared Function TestSystem() As Boolean
        Try
            CreateDataFilesIfNotExists()
            Return File.Exists(UsersFile)
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' إضافة مستخدم جديد
    ''' </summary>
    Public Shared Function AddUser(username As String, password As String, fullName As String, role As String) As Boolean
        Try
            If Not File.Exists(UsersFile) Then
                CreateDataFilesIfNotExists()
            End If
            
            Dim xmlDoc As New XmlDocument()
            xmlDoc.Load(UsersFile)
            
            Dim root As XmlNode = xmlDoc.SelectSingleNode("Users")
            
            ' الحصول على أعلى UserID
            Dim maxId As Integer = 0
            For Each userNode As XmlNode In root.SelectNodes("User")
                Dim currentId As Integer = Convert.ToInt32(GetNodeValue(userNode, "UserID"))
                If currentId > maxId Then
                    maxId = currentId
                End If
            Next
            
            ' إنشاء مستخدم جديد
            Dim newUserElement As XmlElement = xmlDoc.CreateElement("User")
            
            Dim userIdElement As XmlElement = xmlDoc.CreateElement("UserID")
            userIdElement.InnerText = (maxId + 1).ToString()
            newUserElement.AppendChild(userIdElement)
            
            Dim usernameElement As XmlElement = xmlDoc.CreateElement("Username")
            usernameElement.InnerText = username
            newUserElement.AppendChild(usernameElement)
            
            Dim passwordElement As XmlElement = xmlDoc.CreateElement("PasswordHash")
            passwordElement.InnerText = password
            newUserElement.AppendChild(passwordElement)
            
            Dim fullNameElement As XmlElement = xmlDoc.CreateElement("FullName")
            fullNameElement.InnerText = fullName
            newUserElement.AppendChild(fullNameElement)
            
            Dim roleElement As XmlElement = xmlDoc.CreateElement("Role")
            roleElement.InnerText = role
            newUserElement.AppendChild(roleElement)
            
            Dim isActiveElement As XmlElement = xmlDoc.CreateElement("IsActive")
            isActiveElement.InnerText = "1"
            newUserElement.AppendChild(isActiveElement)
            
            Dim createdDateElement As XmlElement = xmlDoc.CreateElement("CreatedDate")
            createdDateElement.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            newUserElement.AppendChild(createdDateElement)
            
            root.AppendChild(newUserElement)
            
            ' حفظ الملف
            xmlDoc.Save(UsersFile)
            
            Return True
            
        Catch ex As Exception
            Throw New Exception("خطأ في إضافة المستخدم: " & ex.Message)
        End Try
    End Function
End Class
