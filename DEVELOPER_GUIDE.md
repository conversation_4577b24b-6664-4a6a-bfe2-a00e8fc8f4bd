# دليل المطور - النظام المحاسبي الموحد

## 🏗️ هيكل المشروع

```
UnifiedAccountingSystem/
├── Database/                    # سكريبتات قاعدة البيانات
│   ├── CreateDatabase.sql      # إنشاء قاعدة البيانات والجداول
│   └── InitialData.sql         # البيانات الأولية
├── DAL/                        # طبقة الوصول للبيانات
│   ├── DatabaseConnection.vb   # إدارة الاتصال بقاعدة البيانات
│   └── EmployeeDAL.vb         # عمليات الموظفين
├── BLL/                        # طبقة منطق الأعمال (قيد التطوير)
├── Models/                     # نماذج البيانات
│   ├── Employee.vb            # نموذج الموظف
│   ├── Department.vb          # نماذج الهيكل التنظيمي
│   └── Salary.vb              # نماذج الرواتب والمحاسبة
├── Forms/                      # واجهات المستخدم
│   ├── LoginForm.vb           # شاشة تسجيل الدخول
│   └── MainForm.vb            # الشاشة الرئيسية
├── Reports/                    # التقارير (قيد التطوير)
└── My Project/                 # ملفات المشروع الأساسية
```

## 🔧 إعداد بيئة التطوير

### المتطلبات
- Visual Studio 2013 أو أحدث
- .NET Framework 4.5
- SQL Server Express 2012 أو أحدث
- Git (للتحكم في الإصدارات)

### خطوات الإعداد
1. استنسخ المشروع من المستودع
2. افتح `UnifiedAccountingSystem.sln` في Visual Studio
3. استعد قاعدة البيانات باستخدام السكريبتات في مجلد `Database/`
4. تأكد من إعدادات الاتصال في `App.config`
5. ابن المشروع (Build → Build Solution)

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية

#### جدول المستخدمين (Users)
```sql
UserID (PK) | Username | Password | FullName | Role | IsActive | CreatedDate | LastLogin
```

#### جدول الموظفين (Employees)
```sql
EmployeeID (PK) | EmployeeCode | FullName | JobTitleID (FK) | GradeID (FK) | 
QualificationID (FK) | DivisionID (FK) | NewSalary | [المخصصات] | [الاستقطاعات]
```

#### الهيكل التنظيمي
- **Departments**: الدوائر
- **Sections**: الأقسام (مرتبطة بالدوائر)
- **Divisions**: الشُعب (مرتبطة بالأقسام)

### العلاقات
- علاقة واحد إلى متعدد بين الدوائر والأقسام
- علاقة واحد إلى متعدد بين الأقسام والشُعب
- علاقة واحد إلى متعدد بين الشُعب والموظفين

## 🎨 معايير التطوير

### تسمية الملفات والفئات
- **النماذج**: `[EntityName].vb` (مثل: `Employee.vb`)
- **DAL**: `[EntityName]DAL.vb` (مثل: `EmployeeDAL.vb`)
- **BLL**: `[EntityName]BLL.vb` (مثل: `EmployeeBLL.vb`)
- **النماذج**: `[FormName]Form.vb` (مثل: `LoginForm.vb`)

### معايير الكود
- استخدم التعليقات باللغة العربية
- اتبع نمط PascalCase للخصائص والدوال
- اتبع نمط camelCase للمتغيرات المحلية
- استخدم أسماء وصفية للمتغيرات والدوال

### مثال على التعليقات:
```vb
''' <summary>
''' الحصول على جميع الموظفين النشطين
''' </summary>
''' <returns>قائمة بالموظفين</returns>
Public Shared Function GetActiveEmployees() As List(Of Employee)
    ' كود الدالة هنا
End Function
```

## 🔌 طبقة الوصول للبيانات (DAL)

### DatabaseConnection.vb
فئة أساسية لإدارة الاتصال بقاعدة البيانات:

```vb
' إنشاء اتصال جديد
Dim connection As SqlConnection = DatabaseConnection.CreateConnection()

' تنفيذ استعلام
Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)

' تنفيذ أمر
Dim rowsAffected As Integer = DatabaseConnection.ExecuteNonQuery(query, parameters)
```

### إضافة DAL جديد
1. أنشئ ملف جديد في مجلد `DAL/`
2. اتبع نمط `[EntityName]DAL.vb`
3. أضف الدوال الأساسية:
   - `GetAll[EntityName]s()` - الحصول على جميع السجلات
   - `Get[EntityName]ById(id)` - الحصول على سجل بالمعرف
   - `Add[EntityName](entity)` - إضافة سجل جديد
   - `Update[EntityName](entity)` - تحديث سجل
   - `Delete[EntityName](id)` - حذف سجل

## 🎯 نماذج البيانات (Models)

### إنشاء نموذج جديد
```vb
Public Class [EntityName]
    ' الخصائص الأساسية
    Public Property ID As Integer
    Public Property Name As String
    Public Property IsActive As Boolean
    Public Property CreatedDate As DateTime
    
    ' منشئ افتراضي
    Public Sub New()
        ID = 0
        Name = ""
        IsActive = True
        CreatedDate = DateTime.Now
    End Sub
    
    ' خصائص محسوبة (إذا لزم الأمر)
    Public ReadOnly Property DisplayName As String
        Get
            Return ID.ToString() & " - " & Name
        End Get
    End Property
End Class
```

## 🖼️ واجهات المستخدم (Forms)

### معايير التصميم
- **الخط**: Cairo 12 للنصوص العربية
- **الاتجاه**: من اليمين إلى اليسار (RTL)
- **الألوان**: 
  - أزرق أساسي: `#3498db`
  - رمادي داكن: `#2c3e50`
  - أخضر: `#27ae60`
  - أحمر: `#e74c3c`

### إنشاء نموذج جديد
1. أضف Windows Form جديد
2. اضبط الخصائص الأساسية:
```vb
Me.RightToLeft = RightToLeft.Yes
Me.RightToLeftLayout = True
Me.Font = New Font("Cairo", 10, FontStyle.Regular)
```

3. أضف معالجات الأحداث المطلوبة
4. طبق التحقق من صحة البيانات

## 🔐 الأمان والصلاحيات

### فئة CurrentUser
```vb
Public Class CurrentUser
    Public Shared Property UserID As Integer
    Public Shared Property Username As String
    Public Shared Property FullName As String
    Public Shared Property Role As String
    
    Public Shared Function HasPermission(permission As String) As Boolean
        ' منطق التحقق من الصلاحيات
    End Function
End Class
```

### التحقق من الصلاحيات
```vb
If CurrentUser.HasPermission("إدارة الموظفين") Then
    ' السماح بالوصول
Else
    MessageBox.Show("ليس لديك صلاحية للوصول لهذه الوظيفة")
End If
```

## 📊 التقارير

### إضافة تقرير جديد
1. أنشئ فئة جديدة في مجلد `Reports/`
2. استخدم Crystal Reports أو تصدير مباشر إلى PDF/Excel
3. أضف معاملات التقرير (التواريخ، المرشحات، إلخ)

### مثال على تصدير Excel:
```vb
Public Shared Sub ExportToExcel(dataTable As DataTable, filePath As String)
    ' كود تصدير البيانات إلى Excel
End Sub
```

## 🧪 الاختبار

### اختبار الوحدة
- اختبر كل دالة في طبقة DAL
- تأكد من صحة العمليات CRUD
- اختبر حالات الخطأ والاستثناءات

### اختبار التكامل
- اختبر تدفق البيانات بين الطبقات
- تأكد من صحة العمليات المحاسبية
- اختبر الأمان والصلاحيات

## 📝 التوثيق

### تعليقات الكود
```vb
''' <summary>
''' وصف مختصر للدالة
''' </summary>
''' <param name="paramName">وصف المعامل</param>
''' <returns>وصف القيمة المرجعة</returns>
''' <exception cref="ExceptionType">متى يحدث الاستثناء</exception>
```

### تحديث README
- أضف أي ميزات جديدة
- حدث تعليمات التثبيت
- أضف أمثلة على الاستخدام

## 🚀 النشر

### إعداد النشر
1. اضبط إعدادات Release في Visual Studio
2. تأكد من تحديث App.config للإنتاج
3. أنشئ حزمة التثبيت

### قائمة التحقق قبل النشر
- [ ] اختبار جميع الوظائف
- [ ] التحقق من الأمان
- [ ] اختبار قاعدة البيانات
- [ ] مراجعة الأداء
- [ ] تحديث التوثيق

## 🔧 استكشاف الأخطاء

### أخطاء شائعة
1. **خطأ الاتصال بقاعدة البيانات**
   - تحقق من نص الاتصال
   - تأكد من تشغيل SQL Server

2. **خطأ في التحويل**
   - استخدم `Convert.To[Type]()` بدلاً من التحويل المباشر
   - تحقق من القيم الفارغة (NULL)

3. **خطأ في الواجهة**
   - تأكد من تطبيق RTL
   - تحقق من ترميز النصوص العربية

## 📞 الدعم

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من التعليقات في الكود
3. راجع ملفات السجلات
4. تواصل مع فريق التطوير

---

**ملاحظة**: يُنصح بإجراء نسخة احتياطية من قاعدة البيانات قبل أي تعديلات كبيرة.
