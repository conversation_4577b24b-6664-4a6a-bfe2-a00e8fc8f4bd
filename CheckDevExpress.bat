@echo off
chcp 65001 >nul
echo ========================================================
echo        🔧 فحص تثبيت DevExpress والمراجع
echo ========================================================
echo.

cd /d "%~dp0"

echo 🔍 البحث عن مكتبات DevExpress...
echo.

set DEVEXPRESS_FOUND=0
set DEVEXPRESS_PATH=""

echo 📂 فحص المسارات المحتملة:

if exist "C:\Program Files (x86)\DevExpress 23.2\Components\Bin\Framework" (
    echo ✅ تم العثور على DevExpress 23.2 في: C:\Program Files (x86)\DevExpress 23.2\Components\Bin\Framework
    set DEVEXPRESS_FOUND=1
    set DEVEXPRESS_PATH=C:\Program Files (x86)\DevExpress 23.2\Components\Bin\Framework
) else (
    echo ❌ لم يتم العثور على DevExpress 23.2 في: C:\Program Files (x86)\DevExpress 23.2\Components\Bin\Framework
)

if exist "C:\Program Files\DevExpress 23.2\Components\Bin\Framework" (
    echo ✅ تم العثور على DevExpress 23.2 في: C:\Program Files\DevExpress 23.2\Components\Bin\Framework
    set DEVEXPRESS_FOUND=1
    set DEVEXPRESS_PATH=C:\Program Files\DevExpress 23.2\Components\Bin\Framework
) else (
    echo ❌ لم يتم العثور على DevExpress 23.2 في: C:\Program Files\DevExpress 23.2\Components\Bin\Framework
)

if exist "C:\Program Files (x86)\DevExpress 22.2\Components\Bin\Framework" (
    echo ✅ تم العثور على DevExpress 22.2 في: C:\Program Files (x86)\DevExpress 22.2\Components\Bin\Framework
    set DEVEXPRESS_FOUND=1
    set DEVEXPRESS_PATH=C:\Program Files (x86)\DevExpress 22.2\Components\Bin\Framework
) else (
    echo ❌ لم يتم العثور على DevExpress 22.2 في: C:\Program Files (x86)\DevExpress 22.2\Components\Bin\Framework
)

if exist "C:\Program Files\DevExpress 22.2\Components\Bin\Framework" (
    echo ✅ تم العثور على DevExpress 22.2 في: C:\Program Files\DevExpress 22.2\Components\Bin\Framework
    set DEVEXPRESS_FOUND=1
    set DEVEXPRESS_PATH=C:\Program Files\DevExpress 22.2\Components\Bin\Framework
) else (
    echo ❌ لم يتم العثور على DevExpress 22.2 في: C:\Program Files\DevExpress 22.2\Components\Bin\Framework
)

echo.

if %DEVEXPRESS_FOUND% equ 1 (
    echo 🎉 تم العثور على DevExpress!
    echo 📍 المسار: %DEVEXPRESS_PATH%
    echo.
    
    echo 🔍 فحص المكتبات المطلوبة:
    if exist "%DEVEXPRESS_PATH%\DevExpress.Utils.v23.2.dll" (
        echo ✅ DevExpress.Utils.v23.2.dll
    ) else if exist "%DEVEXPRESS_PATH%\DevExpress.Utils.v22.2.dll" (
        echo ✅ DevExpress.Utils.v22.2.dll
    ) else (
        echo ❌ DevExpress.Utils.dll غير موجود
    )
    
    if exist "%DEVEXPRESS_PATH%\DevExpress.Data.v23.2.dll" (
        echo ✅ DevExpress.Data.v23.2.dll
    ) else if exist "%DEVEXPRESS_PATH%\DevExpress.Data.v22.2.dll" (
        echo ✅ DevExpress.Data.v22.2.dll
    ) else (
        echo ❌ DevExpress.Data.dll غير موجود
    )
    
    if exist "%DEVEXPRESS_PATH%\DevExpress.XtraEditors.v23.2.dll" (
        echo ✅ DevExpress.XtraEditors.v23.2.dll
    ) else if exist "%DEVEXPRESS_PATH%\DevExpress.XtraEditors.v22.2.dll" (
        echo ✅ DevExpress.XtraEditors.v22.2.dll
    ) else (
        echo ❌ DevExpress.XtraEditors.dll غير موجود
    )
    
    if exist "%DEVEXPRESS_PATH%\DevExpress.XtraGrid.v23.2.dll" (
        echo ✅ DevExpress.XtraGrid.v23.2.dll
    ) else if exist "%DEVEXPRESS_PATH%\DevExpress.XtraGrid.v22.2.dll" (
        echo ✅ DevExpress.XtraGrid.v22.2.dll
    ) else (
        echo ❌ DevExpress.XtraGrid.dll غير موجود
    )
    
    echo.
    echo 🔧 بناء المشروع مع DevExpress...
    
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        echo استخدام Visual Studio 2022...
        "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        echo استخدام Visual Studio 2019...
        "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    ) else (
        echo محاولة استخدام dotnet...
        dotnet build UnifiedAccountingSystem.sln --configuration Debug --verbosity minimal
    )
    
    if %errorlevel% equ 0 (
        echo ✅ تم بناء المشروع بنجاح مع DevExpress!
        echo.
        echo 📋 المراجع المضافة:
        echo    • DevExpress.Utils.v23.2
        echo    • DevExpress.Data.v23.2
        echo    • DevExpress.XtraEditors.v23.2
        echo    • DevExpress.XtraGrid.v23.2
        echo    • DevExpress.XtraLayout.v23.2
        echo    • DevExpress.XtraBars.v23.2
        echo    • DevExpress.XtraNavBar.v23.2
        echo    • DevExpress.XtraReports.v23.2
        echo.
        echo 🎯 يمكنك الآن استخدام:
        echo    • TextEdit بدلاً من TextBox
        echo    • SimpleButton بدلاً من Button
        echo    • GridControl للجداول
        echo    • LayoutControl للتخطيط
        echo    • BarManager للقوائم
        echo.
    ) else (
        echo ❌ فشل في بناء المشروع!
        echo 💡 تأكد من تثبيت DevExpress بشكل صحيح
    )
    
) else (
    echo ❌ لم يتم العثور على DevExpress!
    echo.
    echo 💡 الحلول المقترحة:
    echo    1. تحميل وتثبيت DevExpress من الموقع الرسمي
    echo    2. التأكد من تثبيت الإصدار 22.2 أو 23.2
    echo    3. إعادة تشغيل Visual Studio بعد التثبيت
    echo    4. التحقق من مسار التثبيت
    echo.
    echo 🔗 رابط التحميل:
    echo    https://www.devexpress.com/products/net/controls/winforms/
    echo.
)

echo 📝 ملاحظات مهمة:
echo    • تم إضافة جميع المراجع المطلوبة لملف المشروع
echo    • تم تكوين المسارات التلقائية للعثور على DevExpress
echo    • يدعم الإصدارات 22.2 و 23.2
echo    • تم إضافة Imports للاستخدام المباشر
echo.

pause
