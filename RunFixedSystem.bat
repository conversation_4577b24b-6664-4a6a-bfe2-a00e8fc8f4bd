@echo off
echo تشغيل النظام المحاسبي الموحد المُحدث...
echo =====================================

REM التحقق من وجود Visual Studio
if exist "C:\Program Files (x86)\Microsoft Visual Studio 12.0\Common7\IDE\devenv.exe" (
    echo فتح المشروع في Visual Studio 2013...
    "C:\Program Files (x86)\Microsoft Visual Studio 12.0\Common7\IDE\devenv.exe" UnifiedAccountingSystem.sln
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" (
    echo فتح المشروع في Visual Studio 2022...
    "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" UnifiedAccountingSystem.sln
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" (
    echo فتح المشروع في Visual Studio 2019...
    "C:\Program Files\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" UnifiedAccountingSystem.sln
) else (
    echo لم يتم العثور على Visual Studio
    echo يرجى فتح الملف UnifiedAccountingSystem.sln يدوياً
    pause
)

echo تم إصلاح المشاكل التالية:
echo - إضافة العناصر المفقودة إلى Layout
echo - إصلاح مشكلة SizeType
echo - إصلاح مشكلة ColumnDefinition
echo - إضافة Namespace والـ Imports المطلوبة
echo - إصلاح مراجع CurrentUser
echo - إنشاء Module1.vb لتشغيل التطبيق

pause
