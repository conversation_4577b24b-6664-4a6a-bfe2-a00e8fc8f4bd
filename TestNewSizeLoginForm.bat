@echo off
chcp 65001 >nul
echo ========================================================
echo        📏 شاشة تسجيل الدخول بالحجم الجديد 500x650
echo ========================================================
echo.

cd /d "%~dp0"

echo 📐 الأبعاد الجديدة:
echo    العرض: 500 بكسل
echo    الارتفاع: 650 بكسل
echo    المساحة الإجمالية: أكبر بكثير من السابق
echo.

echo 📍 المواضع الجديدة:
echo    • تسمية اسم المستخدم: (60, 50)
echo    • حقل اسم المستخدم: (60, 80)
echo    • تسمية كلمة المرور: (60, 150)
echo    • حقل كلمة المرور: (60, 180)
echo    • زر الدخول: (300, 280)
echo    • زر الإلغاء: (60, 280)
echo.

echo 🎯 التحسينات:
echo ✅ حجم أكبر للشاشة (500x650)
echo ✅ مساحة أكثر للعناصر
echo ✅ مواضع محسنة ومتباعدة
echo ✅ رؤية أفضل للنصوص
echo ✅ تخطيط أكثر وضوحاً
echo.

echo 🔧 بناء المشروع...
set BUILD_SUCCESS=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2022...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2019...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else (
    echo محاولة استخدام dotnet...
    dotnet build UnifiedAccountingSystem.sln --configuration Debug --verbosity minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
)

if %BUILD_SUCCESS% equ 0 (
    echo.
    echo ❌ فشل في بناء المشروع!
    echo 💡 الحلول المقترحة:
    echo    1. افتح المشروع في Visual Studio
    echo    2. تأكد من تثبيت .NET Framework
    echo    3. شغل المشروع مباشرة بالضغط على F5
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🚀 تشغيل التطبيق...
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    start "" "bin\Debug\UnifiedAccountingSystem.exe"
    echo.
    echo 🎉 تم تشغيل التطبيق بنجاح!
    echo.
    echo 📋 ما يجب أن تراه الآن:
    echo    ✓ شاشة أكبر بحجم 500x650 في وسط الشاشة
    echo    ✓ عنوان "النظام المحاسبي الموحد" في الأعلى
    echo    ✓ مساحة أكبر بين العناصر
    echo    ✓ نص "اسم المستخدم:" في الموضع (60, 50)
    echo    ✓ حقل اسم المستخدم في الموضع (60, 80)
    echo    ✓ نص "كلمة المرور:" في الموضع (60, 150)
    echo    ✓ حقل كلمة المرور في الموضع (60, 180)
    echo    ✓ الأزرار في الأسفل بمواضع متباعدة
    echo    ✓ تخطيط أكثر وضوحاً ونظافة
    echo.
    echo 🎮 للاختبار:
    echo    • لاحظ الحجم الأكبر للشاشة
    echo    • تحقق من وضوح جميع النصوص
    echo    • جرب التفاعل مع العناصر
    echo    • استخدم: admin / admin للدخول
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 يرجى تشغيل المشروع من Visual Studio مباشرة
    echo.
)

echo 📝 بيانات الاختبار:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin
echo.

echo 📏 مقارنة الأحجام:
echo    الحجم السابق: 450x320 = 144,000 بكسل
echo    الحجم الجديد: 500x650 = 325,000 بكسل
echo    الزيادة: أكثر من الضعف!
echo.

pause
