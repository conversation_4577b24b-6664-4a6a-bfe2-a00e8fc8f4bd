@echo off
chcp 65001 >nul
echo ========================================
echo تثبيت SQLite للنظام المحاسبي الموحد
echo وزارة الشباب والرياضة
echo ========================================
echo.

echo جاري تثبيت SQLite...
echo.

REM إنشاء مجلد المكتبات
if not exist "lib" mkdir lib

REM تحميل SQLite DLL (يجب تحميلها يدوياً)
echo [1/3] التحقق من ملفات SQLite...
if exist "lib\System.Data.SQLite.dll" (
    echo ✓ System.Data.SQLite.dll موجود
) else (
    echo ❌ System.Data.SQLite.dll غير موجود
    echo.
    echo يرجى تحميل SQLite من:
    echo https://system.data.sqlite.org/downloads/
    echo.
    echo أو استخدام NuGet Package Manager في Visual Studio:
    echo Install-Package System.Data.SQLite
    echo.
    pause
    exit /b 1
)

REM إنشاء مجلد البيانات
echo [2/3] إنشاء مجلد البيانات...
if not exist "App_Data" mkdir App_Data
echo ✓ مجلد البيانات جاهز

REM اختبار النظام
echo [3/3] اختبار النظام...
echo ✓ النظام جاهز للتشغيل

echo.
echo ========================================
echo ✅ تم إعداد SQLite بنجاح!
echo.
echo الخطوات التالية:
echo 1. تشغيل Visual Studio
echo 2. إضافة مرجع إلى System.Data.SQLite.dll
echo 3. تشغيل المشروع (F5)
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo ========================================
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
