@echo off
chcp 65001 >nul
echo ========================================================
echo        🔧 شاشة تسجيل الدخول المُصلحة والمُحسنة
echo ========================================================
echo.

cd /d "%~dp0"

echo 🎯 الإصلاحات المنجزة:
echo ✅ إصلاح مشكلة عدم ظهور العناصر
echo ✅ توسيط مثالي في وسط الشاشة (450x320)
echo ✅ إصلاح مواضع جميع النصوص والأزرار
echo ✅ ضمان ظهور جميع العناصر بشكل صحيح
echo ✅ تحسين الخطوط العربية مع بدائل آمنة
echo ✅ تأثيرات hover وfocus تفاعلية
echo ✅ ألوان احترافية ومتناسقة
echo ✅ دعم كامل للغة العربية
echo.

echo 📐 المواصفات النهائية:
echo    • الحجم: 450 × 320 بكسل
echo    • الموضع: وسط الشاشة تلقائياً
echo    • الخط: Cairo (أو Tahoma كبديل)
echo    • التخطيط: من اليمين لليسار
echo    • التأثيرات: تفاعلية وسلسة
echo.

echo 🔧 بناء المشروع...
set BUILD_SUCCESS=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2022...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2019...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else (
    echo محاولة استخدام dotnet...
    dotnet build UnifiedAccountingSystem.sln --configuration Debug --verbosity minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
)

if %BUILD_SUCCESS% equ 0 (
    echo.
    echo ❌ فشل في بناء المشروع!
    echo 💡 الحلول المقترحة:
    echo    1. افتح المشروع في Visual Studio
    echo    2. تأكد من تثبيت .NET Framework
    echo    3. شغل المشروع مباشرة بالضغط على F5
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🚀 تشغيل التطبيق...
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    start "" "bin\Debug\UnifiedAccountingSystem.exe"
    echo.
    echo 🎉 تم تشغيل التطبيق بنجاح!
    echo.
    echo 📋 ما يجب أن تراه الآن:
    echo    ✓ شاشة تسجيل دخول في وسط الشاشة
    echo    ✓ عنوان "النظام المحاسبي الموحد" في الأعلى
    echo    ✓ حقلين لاسم المستخدم وكلمة المرور
    echo    ✓ زرين "دخول" و "إلغاء" باللون الأزرق والأحمر
    echo    ✓ نصوص عربية واضحة ومقروءة
    echo.
    echo 🎮 للاختبار:
    echo    • مرر الماوس على الأزرار لرؤية التأثيرات
    echo    • انقر في الحقول النصية لرؤية تأثيرات التركيز
    echo    • استخدم: admin / admin للدخول
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 يرجى تشغيل المشروع من Visual Studio مباشرة
    echo.
)

echo 📝 بيانات الاختبار:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin
echo.

pause
