''' <summary>
''' نموذج بيانات الراتب الشهري
''' </summary>
Public Class MonthlySalary
    Public Property SalaryID As Integer
    Public Property EmployeeID As Integer
    Public Property EmployeeName As String
    Public Property EmployeeCode As String
    Public Property PeriodID As Integer
    Public Property PeriodName As String
    Public Property BasicSalary As Decimal
    Public Property TotalAllowances As Decimal
    Public Property TotalDeductions As Decimal
    Public Property NetSalary As Decimal
    Public Property ProcessedDate As DateTime
    Public Property ProcessedBy As Integer
    Public Property ProcessedByName As String
    
    Public Sub New()
        SalaryID = 0
        EmployeeID = 0
        EmployeeName = ""
        EmployeeCode = ""
        PeriodID = 0
        PeriodName = ""
        BasicSalary = 0
        TotalAllowances = 0
        TotalDeductions = 0
        NetSalary = 0
        ProcessedDate = DateTime.Now
        ProcessedBy = 0
        ProcessedByName = ""
    End Sub
    
    ''' <summary>
    ''' حساب إجمالي الراتب
    ''' </summary>
    Public ReadOnly Property GrossSalary As Decimal
        Get
            Return BasicSalary + TotalAllowances
        End Get
    End Property
End Class

''' <summary>
''' نموذج بيانات البنك
''' </summary>
Public Class Bank
    Public Property BankID As Integer
    Public Property BankName As String
    Public Property BranchName As String
    Public Property BranchCode As String
    Public Property AccountNumber As String
    Public Property AccountType As String
    Public Property IsActive As Boolean
    
    Public Sub New()
        BankID = 0
        BankName = ""
        BranchName = ""
        BranchCode = ""
        AccountNumber = ""
        AccountType = ""
        IsActive = True
    End Sub
End Class

''' <summary>
''' نموذج بيانات العملة
''' </summary>
Public Class Currency
    Public Property CurrencyID As Integer
    Public Property CurrencyCode As String
    Public Property CurrencyName As String
    Public Property ExchangeRate As Decimal
    Public Property IsBaseCurrency As Boolean
    Public Property IsActive As Boolean
    Public Property LastUpdated As DateTime
    
    Public Sub New()
        CurrencyID = 0
        CurrencyCode = ""
        CurrencyName = ""
        ExchangeRate = 1
        IsBaseCurrency = False
        IsActive = True
        LastUpdated = DateTime.Now
    End Sub
End Class

''' <summary>
''' نموذج بيانات دليل المحاسبة
''' </summary>
Public Class AccountingGuide
    Public Property AccountID As Integer
    Public Property FormType As String
    Public Property ExpenseType As String
    Public Property Chapter As String
    Public Property Article As String
    Public Property Type As String
    Public Property TypeDetails As String
    Public Property Description As String
    Public Property PreviousMonthExpenses As Decimal
    Public Property CurrentMonthExpenses As Decimal
    Public Property TotalExpenses As Decimal
    Public Property AnnualAllocation As Decimal
    Public Property IsActive As Boolean
    Public Property CreatedDate As DateTime
    
    Public Sub New()
        AccountID = 0
        FormType = ""
        ExpenseType = ""
        Chapter = ""
        Article = ""
        Type = ""
        TypeDetails = ""
        Description = ""
        PreviousMonthExpenses = 0
        CurrentMonthExpenses = 0
        TotalExpenses = 0
        AnnualAllocation = 0
        IsActive = True
        CreatedDate = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' حساب الرصيد المتبقي
    ''' </summary>
    Public ReadOnly Property RemainingBalance As Decimal
        Get
            Return AnnualAllocation - TotalExpenses
        End Get
    End Property
    
    ''' <summary>
    ''' حساب نسبة الإنفاق
    ''' </summary>
    Public ReadOnly Property SpendingPercentage As Decimal
        Get
            If AnnualAllocation > 0 Then
                Return (TotalExpenses / AnnualAllocation) * 100
            Else
                Return 0
            End If
        End Get
    End Property
End Class

''' <summary>
''' نموذج بيانات الفترة المالية
''' </summary>
Public Class FiscalPeriod
    Public Property PeriodID As Integer
    Public Property PeriodName As String
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property IsActive As Boolean
    Public Property IsClosed As Boolean
    Public Property CreatedDate As DateTime
    
    Public Sub New()
        PeriodID = 0
        PeriodName = ""
        StartDate = Date.Today
        EndDate = Date.Today.AddMonths(12)
        IsActive = True
        IsClosed = False
        CreatedDate = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة الفترة
    ''' </summary>
    Public ReadOnly Property IsValidPeriod As Boolean
        Get
            Return StartDate < EndDate
        End Get
    End Property
    
    ''' <summary>
    ''' التحقق من كون الفترة حالية
    ''' </summary>
    Public ReadOnly Property IsCurrentPeriod As Boolean
        Get
            Dim today As Date = Date.Today
            Return today >= StartDate AndAlso today <= EndDate
        End Get
    End Property
End Class
