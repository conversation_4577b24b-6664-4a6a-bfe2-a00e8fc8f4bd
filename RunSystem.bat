@echo off
chcp 65001 >nul
echo ========================================
echo النظام المحاسبي الموحد
echo وزارة الشباب والرياضة
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود .NET Framework
echo التحقق من .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET Framework 4.5 غير مثبت
    echo يرجى تثبيت .NET Framework 4.5 أو أحدث
    pause
    exit /b 1
)
echo ✓ .NET Framework متوفر

REM التحقق من وجود SQL Server
echo التحقق من SQL Server...
sc query "MSSQL$SQLEXPRESS" >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: SQL Server Express غير مثبت أو غير مُشغل
    echo يرجى تثبيت SQL Server Express وتشغيله
    echo.
    echo هل تريد المتابعة؟ (y/n)
    set /p continue=
    if /i not "%continue%"=="y" exit /b 1
)
echo ✓ SQL Server متوفر

REM التحقق من وجود ملفات النظام
echo التحقق من ملفات النظام...
if not exist "UnifiedAccountingSystem.exe" (
    if not exist "bin\Debug\UnifiedAccountingSystem.exe" (
        if not exist "bin\Release\UnifiedAccountingSystem.exe" (
            echo خطأ: ملف التطبيق غير موجود
            echo يرجى بناء المشروع أولاً باستخدام Visual Studio
            pause
            exit /b 1
        ) else (
            set "APP_PATH=bin\Release\UnifiedAccountingSystem.exe"
        )
    ) else (
        set "APP_PATH=bin\Debug\UnifiedAccountingSystem.exe"
    )
) else (
    set "APP_PATH=UnifiedAccountingSystem.exe"
)
echo ✓ ملفات النظام متوفرة

echo.
echo جاري تشغيل النظام...
echo.

REM تشغيل النظام
start "" "%APP_PATH%"

if %errorlevel% neq 0 (
    echo خطأ في تشغيل النظام
    echo يرجى التحقق من:
    echo 1. تثبيت .NET Framework 4.5
    echo 2. تشغيل SQL Server Express
    echo 3. صحة إعدادات قاعدة البيانات
    pause
    exit /b 1
)

echo تم تشغيل النظام بنجاح!
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
