@echo off
chcp 65001 >nul
echo ========================================
echo        اختبار إصلاح المشكلة
echo ========================================
echo.

cd /d "%~dp0"

echo 🔧 محاولة بناء المشروع...
echo.

REM Try different MSBuild paths
set "VS2013_PATH=C:\Program Files (x86)\Microsoft Visual Studio 12.0\Common7\Tools\VsMSBuildCmd.bat"
set "DOTNET_MSBUILD=C:\Windows\Microsoft.NET\Framework\v4.0.30319\MSBuild.exe"

if exist "%VS2013_PATH%" (
    echo استخدام Visual Studio 2013...
    call "%VS2013_PATH%"
    msbuild UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
) else if exist "%DOTNET_MSBUILD%" (
    echo استخدام .NET Framework MSBuild...
    "%DOTNET_MSBUILD%" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
) else (
    echo محاولة استخدام MSBuild العام...
    msbuild UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
)

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء المشروع بنجاح!
    echo.
    
    if exist "bin\Debug\UnifiedAccountingSystem.exe" (
        echo 🚀 تشغيل التطبيق...
        start "" "bin\Debug\UnifiedAccountingSystem.exe"
        echo.
        echo 🎉 تم تشغيل التطبيق!
        echo.
        echo 📝 بيانات الاختبار:
        echo    اسم المستخدم: admin
        echo    كلمة المرور: admin
        echo.
    ) else (
        echo ❌ لم يتم العثور على الملف التنفيذي
    )
) else (
    echo.
    echo ❌ فشل في بناء المشروع!
    echo 💡 يرجى فتح المشروع في Visual Studio وتشغيله مباشرة
    echo.
)

pause
