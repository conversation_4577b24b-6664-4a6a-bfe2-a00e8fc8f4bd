-- البيانات الأولية للنظام المحاسبي الموحد
-- وزارة الشباب والرياضة

USE [UnifiedAccountingDB];
GO

-- إدراج المستخدم الافتراضي (المدير)
INSERT INTO Users (Username, Password, FullName, Role, IsActive)
VALUES ('admin', 'admin123', N'مدير النظام', N'مدير', 1);

-- إدراج الدوائر
INSERT INTO Departments (DepartmentName, DepartmentCode, Description)
VALUES 
(N'دائرة الطب الرياضي', 'SPM', N'دائرة متخصصة في الطب الرياضي'),
(N'دائرة الشؤون الإدارية', 'ADM', N'دائرة الشؤون الإدارية والمالية'),
(N'دائرة الأنشطة الرياضية', 'SPA', N'دائرة الأنشطة والبرامج الرياضية');

-- إدراج الأقسام
INSERT INTO Sections (SectionName, SectionCode, DepartmentID, Description)
VALUES 
(N'القسم الإداري', 'ADM-01', 1, N'القسم الإداري لدائرة الطب الرياضي'),
(N'القسم الطبي', 'MED-01', 1, N'القسم الطبي المتخصص'),
(N'قسم الموارد البشرية', 'HR-01', 2, N'قسم إدارة الموارد البشرية'),
(N'القسم المالي', 'FIN-01', 2, N'القسم المالي والمحاسبي');

-- إدراج الشُعب
INSERT INTO Divisions (DivisionName, DivisionCode, SectionID, Description)
VALUES 
(N'شعبة الحسابات', 'ACC-01', 1, N'شعبة الحسابات المسؤولة عن صرفيات الدائرة والرواتب'),
(N'شعبة الأرشيف', 'ARC-01', 1, N'شعبة الأرشيف والوثائق'),
(N'شعبة العلاج الطبيعي', 'PHY-01', 2, N'شعبة العلاج الطبيعي'),
(N'شعبة التوظيف', 'REC-01', 3, N'شعبة التوظيف والتعيينات'),
(N'شعبة الرواتب', 'SAL-01', 4, N'شعبة معالجة الرواتب');

-- إدراج العناوين الوظيفية
INSERT INTO JobTitles (TitleName, TitleCode, Description)
VALUES 
(N'مدير عام', 'GM', N'مدير عام الوزارة'),
(N'مدير دائرة', 'DM', N'مدير دائرة'),
(N'رئيس قسم', 'SM', N'رئيس قسم'),
(N'مسؤول شعبة', 'DH', N'مسؤول شعبة'),
(N'موظف إداري', 'AE', N'موظف إداري'),
(N'محاسب', 'ACC', N'محاسب'),
(N'طبيب', 'DOC', N'طبيب متخصص'),
(N'ممرض', 'NUR', N'ممرض'),
(N'سكرتير', 'SEC', N'سكرتير');

-- إدراج الدرجات الوظيفية
INSERT INTO JobGrades (GradeName, GradeLevel, BasicSalary)
VALUES 
(N'خاصة', 1, 2500000),
(N'أولى', 2, 2000000),
(N'ثانية', 3, 1800000),
(N'ثالثة', 4, 1600000),
(N'رابعة', 5, 1400000),
(N'خامسة', 6, 1200000),
(N'سادسة', 7, 1000000),
(N'سابعة', 8, 900000),
(N'ثامنة', 9, 800000),
(N'تاسعة', 10, 700000),
(N'عاشرة', 11, 600000);

-- إدراج الشهادات
INSERT INTO Qualifications (QualificationName, QualificationCode, AllowanceAmount)
VALUES 
(N'دكتوراه', 'PHD', 300000),
(N'ماجستير', 'MSC', 200000),
(N'بكالوريوس', 'BSC', 150000),
(N'دبلوم عالي', 'DIP', 100000),
(N'دبلوم', 'DIPL', 75000),
(N'إعدادية', 'SEC', 50000),
(N'ابتدائية', 'PRI', 25000);

-- إدراج البنوك
INSERT INTO Banks (BankName, BranchName, BranchCode, AccountNumber, AccountType)
VALUES 
(N'مصرف الرافدين', N'فرع دور الضباط', '69', '***************', N'تشغيلي'),
(N'مصرف الرافدين', N'فرع دور الضباط', '69', '***************', N'رواتب');

-- إدراج العملات
INSERT INTO Currencies (CurrencyCode, CurrencyName, ExchangeRate, IsBaseCurrency)
VALUES 
('IQD', N'دينار عراقي', 1, 1),
('USD', N'دولار أمريكي', 1320, 0),
('EUR', N'يورو', 1450, 0);

-- إدراج دليل المحاسبة الأساسي
INSERT INTO AccountingGuide (FormType, ExpenseType, Chapter, Article, Type, TypeDetails, Description, AnnualAllocation)
VALUES 
(N'نفقات تشغيلية', N'رواتب وتعويضات', N'الفصل الأول', N'المادة 1', N'رواتب', N'رواتب الموظفين', N'رواتب الموظفين الثابتين', ********),
(N'نفقات تشغيلية', N'رواتب وتعويضات', N'الفصل الأول', N'المادة 2', N'مخصصات', N'مخصصات الموظفين', N'مخصصات ومكافآت الموظفين', ********),
(N'نفقات تشغيلية', N'السفر والإيفاد', N'الفصل الثاني', N'المادة 1', N'سفر داخلي', N'سفر داخل العراق', N'مصاريف السفر الداخلي', 5000000),
(N'نفقات تشغيلية', N'السفر والإيفاد', N'الفصل الثاني', N'المادة 2', N'سفر خارجي', N'سفر خارج العراق', N'مصاريف السفر الخارجي', 3000000),
(N'نفقات تشغيلية', N'الصيانة والخدمات', N'الفصل الثالث', N'المادة 1', N'صيانة', N'صيانة المباني', N'صيانة المباني والمرافق', 8000000),
(N'نفقات تشغيلية', N'النشر والإعلام', N'الفصل الرابع', N'المادة 1', N'إعلان', N'إعلانات ونشر', N'مصاريف الإعلان والنشر', 2000000);

-- إدراج فترة مالية افتراضية
INSERT INTO FiscalPeriods (PeriodName, StartDate, EndDate, IsActive)
VALUES 
(N'السنة المالية 2024', '2024-01-01', '2024-12-31', 1);

-- إدراج موظف تجريبي
INSERT INTO Employees (
    EmployeeCode, FullName, JobTitleID, GradeID, QualificationID, DivisionID, 
    Stage, NewSalary, PositionAllowance, MaritalAllowance, ChildrenAllowance,
    QualificationAllowance, RetirementDeduction, GovernmentContribution, 
    IncomeTax, HireDate
)
VALUES 
('EMP001', N'أحمد محمد علي', 6, 5, 3, 1, N'المرحلة الأولى', 1400000, 
 200000, 150000, 100000, 150000, 140000, 210000, 50000, '2020-01-15');

PRINT 'تم إدراج البيانات الأولية بنجاح';
GO
