''' <summary>
''' نموذج بيانات الموظف
''' </summary>
Public Class Employee
    Public Property EmployeeID As Integer
    Public Property EmployeeCode As String
    Public Property FullName As String
    Public Property JobTitleID As Integer
    Public Property JobTitleName As String
    Public Property GradeID As Integer
    Public Property GradeName As String
    Public Property QualificationID As Integer?
    Public Property QualificationName As String
    Public Property DivisionID As Integer
    Public Property DivisionName As String
    Public Property SectionName As String
    Public Property DepartmentName As String
    Public Property Stage As String
    Public Property NewSalary As Decimal
    
    ' المخصصات
    Public Property PositionAllowance As Decimal
    Public Property MaritalAllowance As Decimal
    Public Property ChildrenAllowance As Decimal
    Public Property EngineeringAllowance As Decimal
    Public Property QualificationAllowance As Decimal
    Public Property CraftAllowance As Decimal
    Public Property DangerAllowance As Decimal
    Public Property TransportAllowance As Decimal
    Public Property UniversityAllowance As Decimal
    
    ' الاستقطاعات
    Public Property RetirementDeduction As Decimal
    Public Property GovernmentContribution As Decimal
    Public Property IncomeTax As Decimal
    Public Property SocialProtection As Decimal
    Public Property InsuranceInstallments As Decimal
    Public Property ExecutionCircles As Decimal
    Public Property HealthDeposits As Decimal
    
    ' حجوزات البنوك والتنفيذ
    Public Property BankReservations As Decimal
    Public Property ExecutionReservations As Decimal
    
    Public Property HireDate As Date
    Public Property IsActive As Boolean
    Public Property CreatedDate As DateTime
    
    ''' <summary>
    ''' حساب إجمالي المخصصات
    ''' </summary>
    Public ReadOnly Property TotalAllowances As Decimal
        Get
            Return PositionAllowance + MaritalAllowance + ChildrenAllowance + 
                   EngineeringAllowance + QualificationAllowance + CraftAllowance + 
                   DangerAllowance + TransportAllowance + UniversityAllowance
        End Get
    End Property
    
    ''' <summary>
    ''' حساب إجمالي الاستقطاعات
    ''' </summary>
    Public ReadOnly Property TotalDeductions As Decimal
        Get
            Return RetirementDeduction + GovernmentContribution + IncomeTax + 
                   SocialProtection + InsuranceInstallments + ExecutionCircles + 
                   HealthDeposits + BankReservations + ExecutionReservations
        End Get
    End Property
    
    ''' <summary>
    ''' حساب إجمالي الراتب
    ''' </summary>
    Public ReadOnly Property GrossSalary As Decimal
        Get
            Return NewSalary + TotalAllowances
        End Get
    End Property
    
    ''' <summary>
    ''' حساب صافي الراتب
    ''' </summary>
    Public ReadOnly Property NetSalary As Decimal
        Get
            Return GrossSalary - TotalDeductions
        End Get
    End Property
    
    ''' <summary>
    ''' منشئ افتراضي
    ''' </summary>
    Public Sub New()
        EmployeeID = 0
        EmployeeCode = ""
        FullName = ""
        JobTitleID = 0
        JobTitleName = ""
        GradeID = 0
        GradeName = ""
        QualificationID = Nothing
        QualificationName = ""
        DivisionID = 0
        DivisionName = ""
        SectionName = ""
        DepartmentName = ""
        Stage = ""
        NewSalary = 0
        
        ' تهيئة المخصصات
        PositionAllowance = 0
        MaritalAllowance = 0
        ChildrenAllowance = 0
        EngineeringAllowance = 0
        QualificationAllowance = 0
        CraftAllowance = 0
        DangerAllowance = 0
        TransportAllowance = 0
        UniversityAllowance = 0
        
        ' تهيئة الاستقطاعات
        RetirementDeduction = 0
        GovernmentContribution = 0
        IncomeTax = 0
        SocialProtection = 0
        InsuranceInstallments = 0
        ExecutionCircles = 0
        HealthDeposits = 0
        BankReservations = 0
        ExecutionReservations = 0
        
        HireDate = Date.Today
        IsActive = True
        CreatedDate = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' حساب استقطاع التقاعد (10%)
    ''' </summary>
    Public Sub CalculateRetirementDeduction()
        RetirementDeduction = NewSalary * 0.1D
    End Sub
    
    ''' <summary>
    ''' حساب المساهمة الحكومية (15%)
    ''' </summary>
    Public Sub CalculateGovernmentContribution()
        GovernmentContribution = NewSalary * 0.15D
    End Sub
    
    ''' <summary>
    ''' حساب ضريبة الدخل حسب الشرائح
    ''' </summary>
    Public Sub CalculateIncomeTax()
        Dim taxableIncome As Decimal = NewSalary
        
        ' شرائح ضريبة الدخل (مثال)
        If taxableIncome <= 1000000 Then
            IncomeTax = 0
        ElseIf taxableIncome <= 2000000 Then
            IncomeTax = (taxableIncome - 1000000) * 0.05D
        ElseIf taxableIncome <= 3000000 Then
            IncomeTax = 50000 + (taxableIncome - 2000000) * 0.1D
        Else
            IncomeTax = 150000 + (taxableIncome - 3000000) * 0.15D
        End If
    End Sub
    
    ''' <summary>
    ''' تحديث جميع الحسابات التلقائية
    ''' </summary>
    Public Sub UpdateCalculations()
        CalculateRetirementDeduction()
        CalculateGovernmentContribution()
        CalculateIncomeTax()
    End Sub
End Class
