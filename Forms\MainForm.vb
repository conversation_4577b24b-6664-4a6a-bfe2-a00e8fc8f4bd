''' <summary>
''' الشاشة الرئيسية للنظام المحاسبي الموحد
''' </summary>
Public Class MainForm
    
    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        SetupMenus()
        DisplayWelcomeMessage()
    End Sub
    
    ''' <summary>
    ''' تهيئة الشاشة الرئيسية
    ''' </summary>
    Private Sub InitializeForm()
        ' تعيين خصائص النموذج
        Me.Text = "النظام المحاسبي الموحد - وزارة الشباب والرياضة | " & CurrentUser.FullName
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.WindowState = FormWindowState.Maximized
        Me.IsMdiContainer = True
        
        ' تعيين الخط العربي
        Dim arabicFont As New Font("Cairo", 10, FontStyle.Regular)
        Me.Font = arabicFont
        
        ' تطبيق الخط على جميع العناصر
        ApplyFontToControls(Me, arabicFont)
        
        ' تعيين لون الخلفية
        Me.BackColor = Color.FromArgb(236, 240, 241)
    End Sub
    
    ''' <summary>
    ''' تطبيق الخط على جميع العناصر
    ''' </summary>
    Private Sub ApplyFontToControls(parent As Control, font As Font)
        For Each ctrl As Control In parent.Controls
            ctrl.Font = font
            If ctrl.HasChildren Then
                ApplyFontToControls(ctrl, font)
            End If
        Next
    End Sub
    
    ''' <summary>
    ''' إعداد القوائم الرئيسية
    ''' </summary>
    Private Sub SetupMenus()
        ' إنشاء شريط القوائم
        Dim menuStrip As New MenuStrip()
        menuStrip.RightToLeft = RightToLeft.Yes
        menuStrip.Font = New Font("Cairo", 10, FontStyle.Bold)
        menuStrip.BackColor = Color.FromArgb(52, 152, 219)
        menuStrip.ForeColor = Color.White
        
        ' قائمة النظام
        Dim systemMenu As New ToolStripMenuItem("النظام")
        systemMenu.DropDownItems.Add(CreateMenuItem("تسجيل الخروج", AddressOf LogoutMenuItem_Click))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(CreateMenuItem("خروج", AddressOf ExitMenuItem_Click))
        
        ' قائمة المستخدمين
        Dim usersMenu As New ToolStripMenuItem("المستخدمون")
        usersMenu.DropDownItems.Add(CreateMenuItem("إدارة المستخدمين", AddressOf UsersMenuItem_Click))
        usersMenu.DropDownItems.Add(CreateMenuItem("الصلاحيات", AddressOf PermissionsMenuItem_Click))
        
        ' قائمة الإعدادات العامة
        Dim settingsMenu As New ToolStripMenuItem("الإعدادات العامة")
        settingsMenu.DropDownItems.Add(CreateMenuItem("إعدادات النظام", AddressOf SystemSettingsMenuItem_Click))
        settingsMenu.DropDownItems.Add(CreateMenuItem("إعدادات قاعدة البيانات", AddressOf DatabaseSettingsMenuItem_Click))
        
        ' قائمة الموظفون
        Dim employeesMenu As New ToolStripMenuItem("الموظفون")
        employeesMenu.DropDownItems.Add(CreateMenuItem("إدارة الموظفين", AddressOf EmployeesMenuItem_Click))
        employeesMenu.DropDownItems.Add(CreateMenuItem("العناوين الوظيفية", AddressOf JobTitlesMenuItem_Click))
        employeesMenu.DropDownItems.Add(CreateMenuItem("الدرجات الوظيفية", AddressOf JobGradesMenuItem_Click))
        employeesMenu.DropDownItems.Add(CreateMenuItem("الشهادات", AddressOf QualificationsMenuItem_Click))
        employeesMenu.DropDownItems.Add(New ToolStripSeparator())
        employeesMenu.DropDownItems.Add(CreateMenuItem("الدوائر", AddressOf DepartmentsMenuItem_Click))
        employeesMenu.DropDownItems.Add(CreateMenuItem("الأقسام", AddressOf SectionsMenuItem_Click))
        employeesMenu.DropDownItems.Add(CreateMenuItem("الشُعب", AddressOf DivisionsMenuItem_Click))
        
        ' قائمة الحسابات
        Dim accountsMenu As New ToolStripMenuItem("الحسابات")
        accountsMenu.DropDownItems.Add(CreateMenuItem("دليل المحاسبة", AddressOf AccountingGuideMenuItem_Click))
        accountsMenu.DropDownItems.Add(CreateMenuItem("الحسابات البنكية", AddressOf BankAccountsMenuItem_Click))
        accountsMenu.DropDownItems.Add(CreateMenuItem("العملات", AddressOf CurrenciesMenuItem_Click))
        
        ' قائمة الرواتب
        Dim salariesMenu As New ToolStripMenuItem("الرواتب")
        salariesMenu.DropDownItems.Add(CreateMenuItem("معالجة الرواتب", AddressOf ProcessSalariesMenuItem_Click))
        salariesMenu.DropDownItems.Add(CreateMenuItem("كشف الرواتب", AddressOf SalaryReportMenuItem_Click))
        salariesMenu.DropDownItems.Add(CreateMenuItem("تقرير الرواتب الشهري", AddressOf MonthlySalaryReportMenuItem_Click))
        
        ' قائمة التخصيصات المالية
        Dim allocationsMenu As New ToolStripMenuItem("التخصيصات المالية")
        allocationsMenu.DropDownItems.Add(CreateMenuItem("إدارة التخصيصات", AddressOf AllocationsMenuItem_Click))
        allocationsMenu.DropDownItems.Add(CreateMenuItem("تقرير التخصيصات", AddressOf AllocationsReportMenuItem_Click))
        
        ' قائمة الفترات المالية
        Dim periodsMenu As New ToolStripMenuItem("الفترات المالية")
        periodsMenu.DropDownItems.Add(CreateMenuItem("إدارة الفترات", AddressOf PeriodsMenuItem_Click))
        periodsMenu.DropDownItems.Add(CreateMenuItem("إغلاق الفترة", AddressOf ClosePeriodMenuItem_Click))
        
        ' قائمة التقارير
        Dim reportsMenu As New ToolStripMenuItem("التقارير")
        reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير الموظفين", AddressOf EmployeesReportMenuItem_Click))
        reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير الرواتب", AddressOf SalariesReportMenuItem_Click))
        reportsMenu.DropDownItems.Add(CreateMenuItem("ميزان المراجعة", AddressOf TrialBalanceMenuItem_Click))
        reportsMenu.DropDownItems.Add(CreateMenuItem("التقرير المالي الشهري", AddressOf MonthlyFinancialReportMenuItem_Click))
        reportsMenu.DropDownItems.Add(CreateMenuItem("التقرير المالي السنوي", AddressOf AnnualFinancialReportMenuItem_Click))
        
        ' قائمة النسخ الاحتياطي
        Dim backupMenu As New ToolStripMenuItem("النسخ الاحتياطي")
        backupMenu.DropDownItems.Add(CreateMenuItem("إنشاء نسخة احتياطية", AddressOf CreateBackupMenuItem_Click))
        backupMenu.DropDownItems.Add(CreateMenuItem("استعادة نسخة احتياطية", AddressOf RestoreBackupMenuItem_Click))
        backupMenu.DropDownItems.Add(CreateMenuItem("سجل النسخ الاحتياطية", AddressOf BackupHistoryMenuItem_Click))
        
        ' قائمة المساعدة
        Dim helpMenu As New ToolStripMenuItem("المساعدة")
        helpMenu.DropDownItems.Add(CreateMenuItem("دليل المستخدم", AddressOf UserGuideMenuItem_Click))
        helpMenu.DropDownItems.Add(CreateMenuItem("حول النظام", AddressOf AboutMenuItem_Click))
        
        ' إضافة القوائم إلى شريط القوائم
        menuStrip.Items.AddRange({systemMenu, usersMenu, settingsMenu, employeesMenu, 
                                 accountsMenu, salariesMenu, allocationsMenu, periodsMenu, 
                                 reportsMenu, backupMenu, helpMenu})
        
        ' إضافة شريط القوائم إلى النموذج
        Me.MainMenuStrip = menuStrip
        Me.Controls.Add(menuStrip)
    End Sub
    
    ''' <summary>
    ''' إنشاء عنصر قائمة
    ''' </summary>
    Private Function CreateMenuItem(text As String, clickHandler As EventHandler) As ToolStripMenuItem
        Dim menuItem As New ToolStripMenuItem(text)
        menuItem.Font = New Font("Cairo", 9, FontStyle.Regular)
        AddHandler menuItem.Click, clickHandler
        Return menuItem
    End Function
    
    ''' <summary>
    ''' عرض رسالة الترحيب
    ''' </summary>
    Private Sub DisplayWelcomeMessage()
        lblWelcome.Text = "مرحباً بك، " & CurrentUser.FullName
        lblRole.Text = "الدور: " & CurrentUser.Role
        lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm")
    End Sub
    
    ' معالجات أحداث القوائم
    Private Sub LogoutMenuItem_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            CurrentUser.Logout()
            Me.Hide()
            Dim loginForm As New LoginForm()
            loginForm.Show()
        End If
    End Sub
    
    Private Sub ExitMenuItem_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Application.Exit()
        End If
    End Sub
    
    Private Sub UsersMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إدارة المستخدمين قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub PermissionsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الصلاحيات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub SystemSettingsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إعدادات النظام قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub DatabaseSettingsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إعدادات قاعدة البيانات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub EmployeesMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إدارة الموظفين قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub JobTitlesMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة العناوين الوظيفية قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub JobGradesMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الدرجات الوظيفية قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub QualificationsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الشهادات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub DepartmentsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الدوائر قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub SectionsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الأقسام قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub DivisionsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الشُعب قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub AccountingGuideMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة دليل المحاسبة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub BankAccountsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة الحسابات البنكية قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub CurrenciesMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة العملات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub ProcessSalariesMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة معالجة الرواتب قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub SalaryReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير كشف الرواتب قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub MonthlySalaryReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير تقرير الرواتب الشهري قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub AllocationsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إدارة التخصيصات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub AllocationsReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير تقرير التخصيصات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub PeriodsMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إدارة الفترات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub ClosePeriodMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إغلاق الفترة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub EmployeesReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير تقرير الموظفين قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub SalariesReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير تقرير الرواتب قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub TrialBalanceMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير ميزان المراجعة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub MonthlyFinancialReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير التقرير المالي الشهري قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub AnnualFinancialReportMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير التقرير المالي السنوي قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub CreateBackupMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة إنشاء النسخة الاحتياطية قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub RestoreBackupMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير شاشة استعادة النسخة الاحتياطية قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub BackupHistoryMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير سجل النسخ الاحتياطية قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub UserGuideMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تطوير دليل المستخدم قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub AboutMenuItem_Click(sender As Object, e As EventArgs)
        MessageBox.Show("النظام المحاسبي الموحد" & vbCrLf & 
                       "وزارة الشباب والرياضة" & vbCrLf & 
                       "الإصدار 1.0.0" & vbCrLf & 
                       "تم التطوير باستخدام VB.NET", 
                       "حول النظام", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub MainForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If MessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            e.Cancel = True
        End If
    End Sub
End Class
