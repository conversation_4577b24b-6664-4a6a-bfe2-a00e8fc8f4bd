-- إنشاء قاعدة البيانات المحلية للنظام المحاسبي الموحد
-- وزارة الشباب والرياضة

-- جدول الوزارات
CREATE TABLE Ministries (
    MinistryID INT IDENTITY(1,1) PRIMARY KEY,
    MinistryName NVARCHAR(200) NOT NULL,
    MinistryCode NVARCHAR(10) NOT NULL UNIQUE,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الدوائر
CREATE TABLE Departments (
    DepartmentID INT IDENTITY(1,1) PRIMARY KEY,
    DepartmentName NVARCHAR(200) NOT NULL,
    DepartmentCode NVARCHAR(10) NOT NULL,
    MinistryID INT NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (MinistryID) REFERENCES Ministries(MinistryID)
);

-- جدول الأقسام
CREATE TABLE Sections (
    SectionID INT IDENTITY(1,1) PRIMARY KEY,
    SectionName NVARCHAR(200) NOT NULL,
    SectionCode NVARCHAR(10) NOT NULL,
    DepartmentID INT NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- جدول الشعب
CREATE TABLE Divisions (
    DivisionID INT IDENTITY(1,1) PRIMARY KEY,
    DivisionName NVARCHAR(200) NOT NULL,
    DivisionCode NVARCHAR(10) NOT NULL,
    SectionID INT NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (SectionID) REFERENCES Sections(SectionID)
);

-- جدول المسميات الوظيفية
CREATE TABLE JobTitles (
    JobTitleID INT IDENTITY(1,1) PRIMARY KEY,
    TitleName NVARCHAR(200) NOT NULL,
    TitleCode NVARCHAR(10) NOT NULL UNIQUE,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الدرجات الوظيفية
CREATE TABLE JobGrades (
    GradeID INT IDENTITY(1,1) PRIMARY KEY,
    GradeName NVARCHAR(100) NOT NULL,
    GradeNumber INT NOT NULL UNIQUE,
    BasicSalary DECIMAL(18,2) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الشهادات
CREATE TABLE Qualifications (
    QualificationID INT IDENTITY(1,1) PRIMARY KEY,
    QualificationName NVARCHAR(200) NOT NULL,
    QualificationCode NVARCHAR(10) NOT NULL UNIQUE,
    AllowanceAmount DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الموظفين
CREATE TABLE Employees (
    EmployeeID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
    FullName NVARCHAR(200) NOT NULL,
    JobTitleID INT NOT NULL,
    GradeID INT NOT NULL,
    QualificationID INT NULL,
    DivisionID INT NOT NULL,
    Stage NVARCHAR(50) NULL,
    NewSalary DECIMAL(18,2) NOT NULL,
    
    -- المخصصات
    PositionAllowance DECIMAL(18,2) DEFAULT 0,
    MaritalAllowance DECIMAL(18,2) DEFAULT 0,
    ChildrenAllowance DECIMAL(18,2) DEFAULT 0,
    EngineeringAllowance DECIMAL(18,2) DEFAULT 0,
    QualificationAllowance DECIMAL(18,2) DEFAULT 0,
    CraftAllowance DECIMAL(18,2) DEFAULT 0,
    DangerAllowance DECIMAL(18,2) DEFAULT 0,
    TransportAllowance DECIMAL(18,2) DEFAULT 0,
    UniversityAllowance DECIMAL(18,2) DEFAULT 0,
    
    -- الاستقطاعات
    RetirementDeduction DECIMAL(18,2) DEFAULT 0,
    GovernmentContribution DECIMAL(18,2) DEFAULT 0,
    IncomeTax DECIMAL(18,2) DEFAULT 0,
    SocialProtection DECIMAL(18,2) DEFAULT 0,
    InsuranceInstallments DECIMAL(18,2) DEFAULT 0,
    ExecutionCircles DECIMAL(18,2) DEFAULT 0,
    HealthDeposits DECIMAL(18,2) DEFAULT 0,
    BankReservations DECIMAL(18,2) DEFAULT 0,
    ExecutionReservations DECIMAL(18,2) DEFAULT 0,
    
    HireDate DATE NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    
    FOREIGN KEY (JobTitleID) REFERENCES JobTitles(JobTitleID),
    FOREIGN KEY (GradeID) REFERENCES JobGrades(GradeID),
    FOREIGN KEY (QualificationID) REFERENCES Qualifications(QualificationID),
    FOREIGN KEY (DivisionID) REFERENCES Divisions(DivisionID)
);

-- جدول المستخدمين
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(200) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- إدراج البيانات الأولية
INSERT INTO Ministries (MinistryName, MinistryCode) VALUES (N'وزارة الشباب والرياضة', 'MYS');

INSERT INTO Departments (DepartmentName, DepartmentCode, MinistryID) VALUES 
(N'دائرة الطب الرياضي', 'SMD', 1),
(N'دائرة الشؤون الإدارية', 'AAD', 1),
(N'دائرة الأنشطة الرياضية', 'SAD', 1);

INSERT INTO Sections (SectionName, SectionCode, DepartmentID) VALUES 
(N'القسم الإداري', 'AS', 1),
(N'القسم الطبي', 'MS', 1),
(N'قسم الموارد البشرية', 'HRS', 2),
(N'القسم المالي', 'FS', 2);

INSERT INTO Divisions (DivisionName, DivisionCode, SectionID) VALUES 
(N'شعبة الحسابات', 'AD', 1),
(N'شعبة الأرشيف', 'ARD', 1),
(N'شعبة العلاج الطبيعي', 'PTD', 2),
(N'شعبة التوظيف', 'RD', 3),
(N'شعبة الرواتب', 'SD', 4);

INSERT INTO JobTitles (TitleName, TitleCode) VALUES 
(N'مدير عام', 'DG'),
(N'مدير', 'DIR'),
(N'رئيس قسم', 'HD'),
(N'رئيس شعبة', 'DD'),
(N'موظف', 'EMP'),
(N'طبيب اختصاص', 'SP'),
(N'طبيب عام', 'GP');

INSERT INTO JobGrades (GradeName, GradeNumber, BasicSalary) VALUES 
(N'الدرجة الخاصة', 1, 2500000),
(N'الأولى', 2, 2000000),
(N'الثانية', 3, 1800000),
(N'الثالثة', 4, 1600000),
(N'الرابعة', 5, 1400000),
(N'الخامسة', 6, 1200000),
(N'السادسة', 7, 1000000),
(N'السابعة', 8, 900000),
(N'الثامنة', 9, 800000),
(N'التاسعة', 10, 700000);

INSERT INTO Qualifications (QualificationName, QualificationCode, AllowanceAmount) VALUES 
(N'دكتوراه', 'PHD', 300000),
(N'ماجستير', 'MSC', 200000),
(N'بكالوريوس', 'BSC', 150000),
(N'دبلوم عالي', 'DIP', 100000),
(N'دبلوم', 'DIPL', 75000),
(N'إعدادية', 'SEC', 50000),
(N'ابتدائية', 'PRI', 25000);

-- إدراج مستخدم افتراضي
INSERT INTO Users (Username, PasswordHash, FullName, Role) VALUES 
('admin', 'admin123', N'مدير النظام', N'مدير');

PRINT 'تم إنشاء قاعدة البيانات المحلية بنجاح';
