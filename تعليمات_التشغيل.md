# تعليمات تشغيل النظام المحاس<PERSON>ي الموحد

## 🚀 خطوات التشغيل الأولى

### 1. متطلبات النظام
تأكد من توفر المتطلبات التالية:
- Windows 7 أو أحدث
- .NET Framework 4.5 أو أحدث
- SQL Server Express 2012 أو أحدث
- 2 جيجابايت رام على الأقل
- 500 ميجابايت مساحة فارغة على القرص الصلب

### 2. تثبيت قاعدة البيانات

#### أ. تثبيت SQL Server Express
1. قم بتحميل SQL Server Express من موقع Microsoft
2. قم بتثبيته مع الإعدادات الافتراضية
3. تأكد من تشغيل خدمة SQL Server

#### ب. إنشاء قاعدة البيانات
1. افتح SQL Server Management Studio
2. اتصل بالخادم المحلي: `.\SQLEXPRESS`
3. افتح ملف `Database/CreateDatabase.sql`
4. قم بتنفيذ السكريبت لإنشاء قاعدة البيانات
5. افتح ملف `Database/InitialData.sql`
6. قم بتنفيذ السكريبت لإدراج البيانات الأولية

### 3. إعداد التطبيق

#### أ. تحديث إعدادات الاتصال
1. افتح ملف `App.config`
2. تأكد من صحة نص الاتصال:
```xml
<connectionStrings>
    <add name="UnifiedAccountingDB" 
         connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=UnifiedAccountingDB;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

#### ب. بناء المشروع
1. افتح Visual Studio
2. افتح ملف الحل `UnifiedAccountingSystem.sln`
3. اختر Build → Build Solution
4. تأكد من عدم وجود أخطاء في البناء

### 4. التشغيل الأول
1. اضغط F5 أو اختر Debug → Start Debugging
2. ستظهر شاشة تسجيل الدخول
3. استخدم البيانات الافتراضية:
   - **اسم المستخدم**: admin
   - **كلمة المرور**: admin123

## 🔧 إعداد النظام

### 1. إضافة مستخدمين جدد
1. من القائمة الرئيسية اختر "المستخدمون" → "إدارة المستخدمين"
2. اضغط "إضافة مستخدم جديد"
3. أدخل البيانات المطلوبة
4. حدد الدور المناسب (مدير، محاسب، موظف)
5. احفظ البيانات

### 2. إعداد الهيكل التنظيمي

#### أ. إضافة الدوائر
1. اختر "الموظفون" → "الدوائر"
2. أضف الدوائر المطلوبة مثل:
   - دائرة الطب الرياضي
   - دائرة الشؤون الإدارية
   - دائرة الأنشطة الرياضية

#### ب. إضافة الأقسام
1. اختر "الموظفون" → "الأقسام"
2. أضف الأقسام لكل دائرة

#### ج. إضافة الشُعب
1. اختر "الموظفون" → "الشُعب"
2. أضف الشُعب لكل قسم

### 3. إعداد البيانات الأساسية

#### أ. العناوين الوظيفية
1. اختر "الموظفون" → "العناوين الوظيفية"
2. أضف العناوين مثل: مدير عام، مدير دائرة، رئيس قسم، إلخ

#### ب. الدرجات الوظيفية
1. اختر "الموظفون" → "الدرجات الوظيفية"
2. أضف الدرجات من خاصة إلى عاشرة مع الرواتب الأساسية

#### ج. الشهادات
1. اختر "الموظفون" → "الشهادات"
2. أضف الشهادات مع مبالغ المخصصات

## 👥 إدارة الموظفين

### 1. إضافة موظف جديد
1. اختر "الموظفون" → "إدارة الموظفين"
2. اضغط "إضافة موظف جديد"
3. أدخل البيانات الأساسية:
   - رقم الموظف
   - الاسم الكامل
   - العنوان الوظيفي
   - الدرجة الوظيفية
   - الشهادة
   - الشعبة
4. أدخل بيانات الراتب والمخصصات
5. احفظ البيانات

### 2. تحديث بيانات موظف
1. ابحث عن الموظف في قائمة الموظفين
2. اضغط "تعديل"
3. قم بالتعديلات المطلوبة
4. احفظ التغييرات

### 3. حساب الاستقطاعات التلقائية
النظام يحسب تلقائياً:
- **التقاعد**: 10% من الراتب الأساسي
- **المساهمة الحكومية**: 15% من الراتب الأساسي
- **ضريبة الدخل**: حسب الشرائح الضريبية

## 💰 معالجة الرواتب

### 1. معالجة رواتب شهرية
1. اختر "الرواتب" → "معالجة الرواتب"
2. حدد الشهر والسنة
3. اختر الموظفين المطلوب معالجة رواتبهم
4. راجع الحسابات
5. اعتمد الرواتب

### 2. طباعة كشف الرواتب
1. اختر "الرواتب" → "كشف الرواتب"
2. حدد الفترة المطلوبة
3. اختر نوع التقرير (PDF أو Excel)
4. اطبع أو احفظ التقرير

## 📊 التقارير

### 1. تقرير الموظفين
- قائمة شاملة بجميع الموظفين
- تصنيف حسب الدائرة أو القسم
- تصدير بصيغة PDF أو Excel

### 2. التقرير المالي الشهري
- ملخص المصروفات الشهرية
- مقارنة مع التخصيصات
- نسب الإنفاق

### 3. ميزان المراجعة
- أرصدة الحسابات
- الحركات المالية
- التوازن المحاسبي

## 💾 النسخ الاحتياطي

### 1. إنشاء نسخة احتياطية
1. اختر "النسخ الاحتياطي" → "إنشاء نسخة احتياطية"
2. حدد مسار الحفظ
3. أدخل وصف للنسخة
4. اضغط "إنشاء"

### 2. استعادة نسخة احتياطية
1. اختر "النسخ الاحتياطي" → "استعادة نسخة احتياطية"
2. حدد ملف النسخة الاحتياطية
3. تأكد من العملية
4. اضغط "استعادة"

## 🔒 الأمان والصلاحيات

### 1. تغيير كلمة المرور
1. من قائمة النظام اختر "إعدادات المستخدم"
2. أدخل كلمة المرور الحالية
3. أدخل كلمة المرور الجديدة
4. أكد كلمة المرور الجديدة
5. احفظ التغييرات

### 2. إدارة الصلاحيات
- **مدير**: جميع الصلاحيات
- **محاسب**: المحاسبة والرواتب والتقارير
- **موظف**: العرض فقط

## ⚠️ استكشاف الأخطاء

### 1. خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل خدمة SQL Server
- تحقق من صحة نص الاتصال في App.config
- تأكد من وجود قاعدة البيانات

### 2. خطأ في تسجيل الدخول
- تأكد من صحة اسم المستخدم وكلمة المرور
- تحقق من حالة تفعيل المستخدم
- راجع جدول Users في قاعدة البيانات

### 3. بطء في الأداء
- تأكد من توفر ذاكرة كافية
- قم بإعادة فهرسة قاعدة البيانات
- احذف البيانات القديمة غير المطلوبة

## 📞 الدعم التقني

في حالة مواجهة أي مشاكل:
1. راجع ملف السجلات (Logs)
2. تأكد من تحديث النظام
3. تواصل مع فريق الدعم التقني

---

**ملاحظة**: يُنصح بإجراء نسخة احتياطية دورية من قاعدة البيانات لضمان سلامة البيانات.
