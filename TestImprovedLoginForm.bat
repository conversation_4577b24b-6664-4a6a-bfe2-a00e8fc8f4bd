@echo off
chcp 65001 >nul
echo ========================================================
echo           اختبار شاشة تسجيل الدخول المحسنة
echo ========================================================
echo.

cd /d "%~dp0"

echo 🔧 إصلاحات الظهور:
echo ✅ توسيط تلقائي في وسط الشاشة
echo ✅ حجم محسن ومضبوط: 450x320 بكسل
echo ✅ إصلاح مواضع جميع العناصر
echo ✅ ضمان ظهور النصوص والأزرار
echo ✅ تأثيرات hover للأزرار
echo ✅ تأثيرات focus للحقول النصية
echo ✅ ألوان احترافية ومتناسقة
echo ✅ خط Cairo العربي المحسن
echo.

echo 🔧 بناء المشروع...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
) else (
    echo ⚠️  لم يتم العثور على MSBuild، جاري المحاولة مع dotnet...
    dotnet build UnifiedAccountingSystem.sln --configuration Debug --verbosity minimal
)

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع!
    echo 💡 يرجى فتح المشروع في Visual Studio وتشغيله مباشرة
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🚀 تشغيل التطبيق...
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    start "" "bin\Debug\UnifiedAccountingSystem.exe"
    echo.
    echo 🎉 تم تشغيل التطبيق بنجاح!
    echo.
    echo 📋 ميزات الشاشة الجديدة:
    echo    • الشاشة ستظهر في وسط الشاشة تلقائياً
    echo    • مرر الماوس على الأزرار لرؤية التأثيرات
    echo    • انقر في الحقول النصية لرؤية تأثيرات التركيز
    echo    • تصميم احترافي بألوان متناسقة
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 يرجى تشغيل المشروع من Visual Studio
    echo.
)

echo 📝 بيانات الاختبار:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin
echo.

pause
