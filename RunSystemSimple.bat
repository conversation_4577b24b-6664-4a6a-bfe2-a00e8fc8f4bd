@echo off
chcp 65001 >nul
echo ========================================
echo تشغيل النظام المحاسبي الموحد
echo وزارة الشباب والرياضة
echo ========================================
echo.

echo جاري تشغيل النظام...
echo.

REM إنشاء مجلد البيانات
if not exist "Data" mkdir Data

REM التحقق من ملفات النظام
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    echo ✓ ملف النظام موجود
    echo جاري التشغيل...
    echo.
    
    REM تشغيل النظام
    start "" "bin\Debug\UnifiedAccountingSystem.exe"
    
    echo ✅ تم تشغيل النظام بنجاح!
    echo.
    echo بيانات الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    
) else (
    echo ❌ ملف النظام غير موجود
    echo.
    echo يرجى:
    echo 1. فتح Visual Studio
    echo 2. بناء المشروع (Build Solution)
    echo 3. تشغيل المشروع (F5)
    echo.
    echo أو استخدام:
    echo dotnet build
    echo dotnet run
    echo.
)

echo اضغط أي مفتاح للإغلاق...
pause >nul
