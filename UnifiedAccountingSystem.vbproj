﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>UnifiedAccountingSystem.My.MyApplication</StartupObject>
    <RootNamespace>UnifiedAccountingSystem</RootNamespace>
    <AssemblyName>UnifiedAccountingSystem</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>UnifiedAccountingSystem.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>UnifiedAccountingSystem.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <!-- DevExpress Installation Path -->
    <DevExpressPath Condition="'$(DevExpressPath)' == ''">C:\Program Files (x86)\DevExpress 23.2\Components\Bin\Framework</DevExpressPath>
    <DevExpressPath Condition="!Exists('$(DevExpressPath)') And Exists('C:\Program Files\DevExpress 23.2\Components\Bin\Framework')">C:\Program Files\DevExpress 23.2\Components\Bin\Framework</DevExpressPath>
    <DevExpressPath Condition="!Exists('$(DevExpressPath)') And Exists('C:\Program Files (x86)\DevExpress 22.2\Components\Bin\Framework')">C:\Program Files (x86)\DevExpress 22.2\Components\Bin\Framework</DevExpressPath>
    <DevExpressPath Condition="!Exists('$(DevExpressPath)') And Exists('C:\Program Files\DevExpress 22.2\Components\Bin\Framework')">C:\Program Files\DevExpress 22.2\Components\Bin\Framework</DevExpressPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Configuration" />
    <!-- DevExpress References -->
    <Reference Include="DevExpress.Utils.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.Utils.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Data.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.Data.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.XtraEditors.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.XtraGrid.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.XtraLayout.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.XtraBars.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.XtraNavBar.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v23.2">
      <HintPath>$(DevExpressPath)\DevExpress.XtraReports.v23.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
    <!-- DevExpress Imports -->
    <Import Include="DevExpress.XtraEditors" />
    <Import Include="DevExpress.XtraGrid" />
    <Import Include="DevExpress.XtraGrid.Views.Grid" />
    <Import Include="DevExpress.XtraLayout" />
    <Import Include="DevExpress.XtraBars" />
    <Import Include="DevExpress.XtraNavBar" />
    <Import Include="DevExpress.Utils" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DAL\DatabaseConnection.vb" />
    <Compile Include="DAL\EmployeeDAL.vb" />
    <Compile Include="DAL\SimpleDatabaseConnection.vb" />
    <Compile Include="DAL\XMLDatabaseConnection.vb" />
    <Compile Include="Forms\LoginForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.vb">
      <DependentUpon>LoginForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DevExpressLoginForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Models\Department.vb" />
    <Compile Include="Models\Employee.vb" />
    <Compile Include="Models\Salary.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\UnifiedAccountingSystem.exe.config" />
    <None Include="bin\Debug\UnifiedAccountingSystem.vshost.exe.config" />
    <None Include="CheckSystem.bat" />
    <None Include="DEVELOPER_GUIDE.md" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="bin\Release\" />
    <Folder Include="BLL\" />
    <Folder Include="Reports\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bin\Debug\UnifiedAccountingSystem.exe" />
    <Content Include="bin\Debug\UnifiedAccountingSystem.pdb" />
    <Content Include="bin\Debug\UnifiedAccountingSystem.vshost.exe" />
    <Content Include="bin\Debug\UnifiedAccountingSystem.xml" />
    <Content Include="Database\CreateDatabase.sql" />
    <Content Include="Database\InitialData.sql" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>