@echo off
chcp 65001 >nul
echo ========================================================
echo        🎨 اختبار تكامل DevExpress مع النظام
echo ========================================================
echo.

cd /d "%~dp0"

echo 📋 ما تم إضافته:
echo ✅ مراجع DevExpress إلى ملف المشروع
echo ✅ مسارات تلقائية للعثور على DevExpress
echo ✅ Imports للاستخدام المباشر
echo ✅ شاشة تسجيل دخول جديدة بـ DevExpress
echo ✅ دعم الإصدارات 22.2 و 23.2
echo.

echo 🔧 المراجع المضافة:
echo    • DevExpress.Utils.v23.2
echo    • DevExpress.Data.v23.2  
echo    • DevExpress.XtraEditors.v23.2
echo    • DevExpress.XtraGrid.v23.2
echo    • DevExpress.XtraLayout.v23.2
echo    • DevExpress.XtraBars.v23.2
echo    • DevExpress.XtraNavBar.v23.2
echo    • DevExpress.XtraReports.v23.2
echo.

echo 🎨 مميزات شاشة DevExpress الجديدة:
echo    • XtraForm بدلاً من Form عادي
echo    • LayoutControl للتخطيط التلقائي
echo    • TextEdit بدلاً من TextBox
echo    • SimpleButton بدلاً من Button عادي
echo    • LabelControl للنصوص
echo    • ثيم Office 2019 Colorful
echo    • تخطيط متجاوب ومرن
echo    • XtraMessageBox للرسائل
echo.

echo 🔍 فحص تثبيت DevExpress...
call CheckDevExpress.bat

echo.
echo 🚀 بناء المشروع مع DevExpress...

set BUILD_SUCCESS=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2022...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2019...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else (
    echo محاولة استخدام dotnet...
    dotnet build UnifiedAccountingSystem.sln --configuration Debug --verbosity minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
)

if %BUILD_SUCCESS% equ 1 (
    echo ✅ تم بناء المشروع بنجاح مع DevExpress!
    echo.
    
    echo 🎯 الآن يمكنك:
    echo    1. استخدام الشاشة العادية: LoginForm
    echo    2. استخدام شاشة DevExpress: DevExpressLoginForm
    echo    3. إنشاء شاشات جديدة بـ DevExpress
    echo    4. استخدام جميع عناصر DevExpress
    echo.
    
    echo 💡 للتبديل إلى شاشة DevExpress:
    echo    • افتح Module1.vb أو Main
    echo    • غير LoginForm إلى DevExpressLoginForm
    echo    • أو أنشئ شاشة اختبار منفصلة
    echo.
    
    echo 📚 عناصر DevExpress المتاحة الآن:
    echo    • TextEdit - حقول النص المحسنة
    echo    • SimpleButton - أزرار أنيقة
    echo    • GridControl - جداول متقدمة
    echo    • LayoutControl - تخطيط تلقائي
    echo    • BarManager - قوائم وأشرطة أدوات
    echo    • NavBarControl - شريط التنقل
    echo    • XtraForm - شاشات محسنة
    echo    • XtraMessageBox - رسائل أنيقة
    echo.
    
) else (
    echo ❌ فشل في بناء المشروع!
    echo.
    echo 💡 الأسباب المحتملة:
    echo    1. DevExpress غير مثبت
    echo    2. إصدار DevExpress غير متوافق
    echo    3. مسار DevExpress غير صحيح
    echo    4. ترخيص DevExpress منتهي الصلاحية
    echo.
    echo 🔧 الحلول:
    echo    1. تثبيت DevExpress من الموقع الرسمي
    echo    2. التأكد من الترخيص
    echo    3. إعادة تشغيل Visual Studio
    echo    4. تحديث مسارات المراجع يدوياً
    echo.
)

echo 📝 ملاحظات مهمة:
echo    • تم تكوين المشروع للعمل مع أو بدون DevExpress
echo    • الشاشة العادية ستعمل حتى لو لم يكن DevExpress مثبت
echo    • شاشة DevExpress تحتاج تثبيت DevExpress
echo    • يمكن التبديل بين الشاشتين بسهولة
echo.

echo 🎨 مثال للاستخدام:
echo    Dim devForm As New DevExpressLoginForm()
echo    If devForm.ShowDialog() = DialogResult.OK Then
echo        ' تم تسجيل الدخول بنجاح
echo    End If
echo.

pause
