Imports System.Windows.Forms

''' <summary>
''' ملف اختبار لتوسيط الشاشة
''' </summary>
Module TestCenterForm
    
    ''' <summary>
    ''' اختبار توسيط شاشة تسجيل الدخول
    ''' </summary>
    Sub TestLoginFormCentering()
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)
        
        Dim loginForm As New LoginForm()
        
        ' إظهار معلومات الشاشة قبل التوسيط
        Console.WriteLine("معلومات الشاشة:")
        Console.WriteLine($"عرض الشاشة: {Screen.PrimaryScreen.WorkingArea.Width}")
        Console.WriteLine($"ارتفاع الشاشة: {Screen.PrimaryScreen.WorkingArea.Height}")
        Console.WriteLine($"عرض النموذج: {loginForm.Width}")
        Console.WriteLine($"ارتفاع النموذج: {loginForm.Height}")
        
        ' حساب الموضع المركزي
        Dim centerX As Integer = (Screen.PrimaryScreen.WorkingArea.Width - loginForm.Width) \ 2
        Dim centerY As Integer = (Screen.PrimaryScreen.WorkingArea.Height - loginForm.Height) \ 2
        
        Console.WriteLine($"الموضع المركزي المحسوب: X={centerX}, Y={centerY}")
        
        Application.Run(loginForm)
    End Sub
    
End Module
