@echo off
chcp 65001 >nul
echo ========================================================
echo        🔧 إصلاح مشكلة ظهور العناصر - الإصدار النهائي
echo ========================================================
echo.

cd /d "%~dp0"

echo 🎯 الإصلاحات المطبقة:
echo ✅ نقل التسميات إلى المواضع الصحيحة
echo ✅ تغيير الخط إلى Tahoma (أكثر وضوحاً)
echo ✅ إضافة BringToFront للتسميات
echo ✅ ضمان ظهور جميع العناصر
echo ✅ توسيط مثالي في وسط الشاشة
echo ✅ تأثيرات تفاعلية محسنة
echo.

echo 📐 المواضع الجديدة:
echo    • تسمية اسم المستخدم: (43, 10)
echo    • حقل اسم المستخدم: (43, 30)
echo    • تسمية كلمة المرور: (43, 65)
echo    • حقل كلمة المرور: (43, 85)
echo    • الأزرار: (287, 140) و (43, 140)
echo.

echo 🔧 بناء المشروع...
set BUILD_SUCCESS=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2022...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2019...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
) else (
    echo محاولة استخدام dotnet...
    dotnet build UnifiedAccountingSystem.sln --configuration Debug --verbosity minimal
    if %errorlevel% equ 0 set BUILD_SUCCESS=1
)

if %BUILD_SUCCESS% equ 0 (
    echo.
    echo ❌ فشل في بناء المشروع!
    echo 💡 الحلول المقترحة:
    echo    1. افتح المشروع في Visual Studio
    echo    2. تأكد من تثبيت .NET Framework
    echo    3. شغل المشروع مباشرة بالضغط على F5
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🚀 تشغيل التطبيق...
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    start "" "bin\Debug\UnifiedAccountingSystem.exe"
    echo.
    echo 🎉 تم تشغيل التطبيق بنجاح!
    echo.
    echo 📋 ما يجب أن تراه الآن:
    echo    ✓ شاشة تسجيل دخول في وسط الشاشة
    echo    ✓ عنوان "النظام المحاسبي الموحد" في الأعلى
    echo    ✓ نص "اسم المستخدم:" مرئي أعلى الحقل الأول
    echo    ✓ نص "كلمة المرور:" مرئي أعلى الحقل الثاني
    echo    ✓ حقلين للإدخال واضحين ومرئيين
    echo    ✓ زرين "دخول" (أزرق) و "إلغاء" (أحمر)
    echo    ✓ جميع النصوص العربية واضحة ومقروءة
    echo.
    echo 🎮 للاختبار:
    echo    • تأكد من ظهور جميع النصوص العربية
    echo    • مرر الماوس على الأزرار لرؤية التأثيرات
    echo    • انقر في الحقول لرؤية تأثيرات التركيز
    echo    • استخدم: admin / admin للدخول
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 يرجى تشغيل المشروع من Visual Studio مباشرة
    echo.
)

echo 📝 بيانات الاختبار:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin
echo.

echo 🔍 إذا لم تظهر النصوص:
echo    1. تأكد من تثبيت خط Tahoma
echo    2. جرب تشغيل Visual Studio كمدير
echo    3. تحقق من إعدادات العرض في Windows
echo.

pause
