-- إنشاء قاعدة البيانات للنظام المحاسبي الموحد
-- وزارة الشباب والرياضة

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'UnifiedAccountingDB')
BEGIN
    CREATE DATABASE [UnifiedAccountingDB]
    ON (NAME = 'UnifiedAccountingDB', FILENAME = 'UnifiedAccountingDB.mdf')
    LOG ON (NAME = 'UnifiedAccountingDB_Log', FILENAME = 'UnifiedAccountingDB_Log.ldf');
END
GO

USE [UnifiedAccountingDB];
GO

-- جدول المستخدمين
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Password NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastLogin DATETIME NULL
);

-- جدول الدوائر
CREATE TABLE Departments (
    DepartmentID INT IDENTITY(1,1) PRIMARY KEY,
    DepartmentName NVARCHAR(100) NOT NULL,
    DepartmentCode NVARCHAR(20) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الأقسام
CREATE TABLE Sections (
    SectionID INT IDENTITY(1,1) PRIMARY KEY,
    SectionName NVARCHAR(100) NOT NULL,
    SectionCode NVARCHAR(20) NOT NULL,
    DepartmentID INT NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- جدول الشُعب
CREATE TABLE Divisions (
    DivisionID INT IDENTITY(1,1) PRIMARY KEY,
    DivisionName NVARCHAR(100) NOT NULL,
    DivisionCode NVARCHAR(20) NOT NULL,
    SectionID INT NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (SectionID) REFERENCES Sections(SectionID)
);

-- جدول العناوين الوظيفية
CREATE TABLE JobTitles (
    JobTitleID INT IDENTITY(1,1) PRIMARY KEY,
    TitleName NVARCHAR(100) NOT NULL,
    TitleCode NVARCHAR(20) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1
);

-- جدول الدرجات الوظيفية
CREATE TABLE JobGrades (
    GradeID INT IDENTITY(1,1) PRIMARY KEY,
    GradeName NVARCHAR(50) NOT NULL,
    GradeLevel INT NOT NULL,
    BasicSalary DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1
);

-- جدول الشهادات
CREATE TABLE Qualifications (
    QualificationID INT IDENTITY(1,1) PRIMARY KEY,
    QualificationName NVARCHAR(100) NOT NULL,
    QualificationCode NVARCHAR(20) NOT NULL UNIQUE,
    AllowanceAmount DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1
);

-- جدول الموظفين
CREATE TABLE Employees (
    EmployeeID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
    FullName NVARCHAR(100) NOT NULL,
    JobTitleID INT NOT NULL,
    GradeID INT NOT NULL,
    QualificationID INT NULL,
    DivisionID INT NOT NULL,
    Stage NVARCHAR(50),
    NewSalary DECIMAL(18,2) NOT NULL,
    
    -- المخصصات
    PositionAllowance DECIMAL(18,2) DEFAULT 0,
    MaritalAllowance DECIMAL(18,2) DEFAULT 0,
    ChildrenAllowance DECIMAL(18,2) DEFAULT 0,
    EngineeringAllowance DECIMAL(18,2) DEFAULT 0,
    QualificationAllowance DECIMAL(18,2) DEFAULT 0,
    CraftAllowance DECIMAL(18,2) DEFAULT 0,
    DangerAllowance DECIMAL(18,2) DEFAULT 0,
    TransportAllowance DECIMAL(18,2) DEFAULT 0,
    UniversityAllowance DECIMAL(18,2) DEFAULT 0,
    
    -- الاستقطاعات
    RetirementDeduction DECIMAL(18,2) DEFAULT 0,
    GovernmentContribution DECIMAL(18,2) DEFAULT 0,
    IncomeTax DECIMAL(18,2) DEFAULT 0,
    SocialProtection DECIMAL(18,2) DEFAULT 0,
    InsuranceInstallments DECIMAL(18,2) DEFAULT 0,
    ExecutionCircles DECIMAL(18,2) DEFAULT 0,
    HealthDeposits DECIMAL(18,2) DEFAULT 0,
    
    -- حجوزات البنوك والتنفيذ
    BankReservations DECIMAL(18,2) DEFAULT 0,
    ExecutionReservations DECIMAL(18,2) DEFAULT 0,
    
    HireDate DATE NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    
    FOREIGN KEY (JobTitleID) REFERENCES JobTitles(JobTitleID),
    FOREIGN KEY (GradeID) REFERENCES JobGrades(GradeID),
    FOREIGN KEY (QualificationID) REFERENCES Qualifications(QualificationID),
    FOREIGN KEY (DivisionID) REFERENCES Divisions(DivisionID)
);

-- جدول البنوك
CREATE TABLE Banks (
    BankID INT IDENTITY(1,1) PRIMARY KEY,
    BankName NVARCHAR(100) NOT NULL,
    BranchName NVARCHAR(100),
    BranchCode NVARCHAR(20),
    AccountNumber NVARCHAR(50) NOT NULL,
    AccountType NVARCHAR(50) NOT NULL, -- تشغيلي، رواتب، إلخ
    IsActive BIT DEFAULT 1
);

-- جدول العملات
CREATE TABLE Currencies (
    CurrencyID INT IDENTITY(1,1) PRIMARY KEY,
    CurrencyCode NVARCHAR(10) NOT NULL UNIQUE,
    CurrencyName NVARCHAR(50) NOT NULL,
    ExchangeRate DECIMAL(18,6) DEFAULT 1,
    IsBaseCurrency BIT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    LastUpdated DATETIME DEFAULT GETDATE()
);

-- جدول دليل المحاسبة
CREATE TABLE AccountingGuide (
    AccountID INT IDENTITY(1,1) PRIMARY KEY,
    FormType NVARCHAR(50) NOT NULL,
    ExpenseType NVARCHAR(50) NOT NULL,
    Chapter NVARCHAR(50) NOT NULL,
    Article NVARCHAR(50) NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    TypeDetails NVARCHAR(100),
    Description NVARCHAR(255) NOT NULL,
    PreviousMonthExpenses DECIMAL(18,2) DEFAULT 0,
    CurrentMonthExpenses DECIMAL(18,2) DEFAULT 0,
    TotalExpenses DECIMAL(18,2) DEFAULT 0,
    AnnualAllocation DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الفترات المالية
CREATE TABLE FiscalPeriods (
    PeriodID INT IDENTITY(1,1) PRIMARY KEY,
    PeriodName NVARCHAR(50) NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    IsActive BIT DEFAULT 1,
    IsClosed BIT DEFAULT 0,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الرواتب الشهرية
CREATE TABLE MonthlySalaries (
    SalaryID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    PeriodID INT NOT NULL,
    BasicSalary DECIMAL(18,2) NOT NULL,
    TotalAllowances DECIMAL(18,2) DEFAULT 0,
    TotalDeductions DECIMAL(18,2) DEFAULT 0,
    NetSalary DECIMAL(18,2) NOT NULL,
    ProcessedDate DATETIME DEFAULT GETDATE(),
    ProcessedBy INT NOT NULL,
    
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (PeriodID) REFERENCES FiscalPeriods(PeriodID),
    FOREIGN KEY (ProcessedBy) REFERENCES Users(UserID)
);

-- جدول النسخ الاحتياطية
CREATE TABLE BackupHistory (
    BackupID INT IDENTITY(1,1) PRIMARY KEY,
    BackupPath NVARCHAR(255) NOT NULL,
    BackupSize BIGINT,
    BackupType NVARCHAR(50) NOT NULL, -- يدوي، تلقائي
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME DEFAULT GETDATE(),
    
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- إنشاء الفهارس
CREATE INDEX IX_Employees_EmployeeCode ON Employees(EmployeeCode);
CREATE INDEX IX_Employees_DivisionID ON Employees(DivisionID);
CREATE INDEX IX_MonthlySalaries_EmployeeID ON MonthlySalaries(EmployeeID);
CREATE INDEX IX_MonthlySalaries_PeriodID ON MonthlySalaries(PeriodID);

PRINT 'تم إنشاء قاعدة البيانات بنجاح';
GO
