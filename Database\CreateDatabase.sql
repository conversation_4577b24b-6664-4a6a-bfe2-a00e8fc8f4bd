-- إنشاء قاعدة البيانات للنظام المحاسبي الموحد
-- وزارة الشباب والرياضة

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'UnifiedAccountingDB')
BEGIN
    CREATE DATABASE [UnifiedAccountingDB]
    ON (NAME = 'UnifiedAccountingDB', FILENAME = 'UnifiedAccountingDB.mdf')
    LOG ON (NAME = 'UnifiedAccountingDB_Log', FILENAME = 'UnifiedAccountingDB_Log.ldf');
END
GO

USE [UnifiedAccountingDB];
GO

-- جدول المستخدمين
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Password NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastLogin DATETIME NULL
);

-- جدول الدوائر
CREATE TABLE Departments (
    DepartmentID INT IDENTITY(1,1) PRIMARY KEY,
    DepartmentName NVARCHAR(100) NOT NULL,
    DepartmentCode NVARCHAR(20) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الأقسام
CREATE TABLE Sections (
    SectionID INT IDENTITY(1,1) PRIMARY KEY,
    SectionName NVARCHAR(100) NOT NULL,
    SectionCode NVARCHAR(20) NOT NULL,
    DepartmentID INT NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- جدول الشُعب
CREATE TABLE Divisions (
    DivisionID INT IDENTITY(1,1) PRIMARY KEY,
    DivisionName NVARCHAR(100) NOT NULL,
    DivisionCode NVARCHAR(20) NOT NULL,
    SectionID INT NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (SectionID) REFERENCES Sections(SectionID)
);

-- جدول العناوين الوظيفية
CREATE TABLE JobTitles (
    JobTitleID INT IDENTITY(1,1) PRIMARY KEY,
    TitleName NVARCHAR(100) NOT NULL,
    TitleCode NVARCHAR(20) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1
);

-- جدول الدرجات الوظيفية
CREATE TABLE JobGrades (
    GradeID INT IDENTITY(1,1) PRIMARY KEY,
    GradeName NVARCHAR(50) NOT NULL,
    GradeLevel INT NOT NULL,
    BasicSalary DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1
);

-- جدول الشهادات
CREATE TABLE Qualifications (
    QualificationID INT IDENTITY(1,1) PRIMARY KEY,
    QualificationName NVARCHAR(100) NOT NULL,
    QualificationCode NVARCHAR(20) NOT NULL UNIQUE,
    AllowanceAmount DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1
);

-- جدول الموظفين
CREATE TABLE Employees (
    EmployeeID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
    FullName NVARCHAR(100) NOT NULL,
    JobTitleID INT NOT NULL,
    GradeID INT NOT NULL,
    QualificationID INT NULL,
    DivisionID INT NOT NULL,
    Stage NVARCHAR(50),
    NewSalary DECIMAL(18,2) NOT NULL,
    
    -- المخصصات
    PositionAllowance DECIMAL(18,2) DEFAULT 0,
    MaritalAllowance DECIMAL(18,2) DEFAULT 0,
    ChildrenAllowance DECIMAL(18,2) DEFAULT 0,
    EngineeringAllowance DECIMAL(18,2) DEFAULT 0,
    QualificationAllowance DECIMAL(18,2) DEFAULT 0,
    CraftAllowance DECIMAL(18,2) DEFAULT 0,
    DangerAllowance DECIMAL(18,2) DEFAULT 0,
    TransportAllowance DECIMAL(18,2) DEFAULT 0,
    UniversityAllowance DECIMAL(18,2) DEFAULT 0,
    
    -- الاستقطاعات
    RetirementDeduction DECIMAL(18,2) DEFAULT 0,
    GovernmentContribution DECIMAL(18,2) DEFAULT 0,
    IncomeTax DECIMAL(18,2) DEFAULT 0,
    SocialProtection DECIMAL(18,2) DEFAULT 0,
    InsuranceInstallments DECIMAL(18,2) DEFAULT 0,
    ExecutionCircles DECIMAL(18,2) DEFAULT 0,
    HealthDeposits DECIMAL(18,2) DEFAULT 0,
    
    -- حجوزات البنوك والتنفيذ
    BankReservations DECIMAL(18,2) DEFAULT 0,
    ExecutionReservations DECIMAL(18,2) DEFAULT 0,
    
    HireDate DATE NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    
    FOREIGN KEY (JobTitleID) REFERENCES JobTitles(JobTitleID),
    FOREIGN KEY (GradeID) REFERENCES JobGrades(GradeID),
    FOREIGN KEY (QualificationID) REFERENCES Qualifications(QualificationID),
    FOREIGN KEY (DivisionID) REFERENCES Divisions(DivisionID)
);

-- جدول البنوك
CREATE TABLE Banks (
    BankID INT IDENTITY(1,1) PRIMARY KEY,
    BankName NVARCHAR(100) NOT NULL,
    BranchName NVARCHAR(100),
    BranchCode NVARCHAR(20),
    AccountNumber NVARCHAR(50) NOT NULL,
    AccountType NVARCHAR(50) NOT NULL, -- تشغيلي، رواتب، إلخ
    IsActive BIT DEFAULT 1
);

-- جدول العملات
CREATE TABLE Currencies (
    CurrencyID INT IDENTITY(1,1) PRIMARY KEY,
    CurrencyCode NVARCHAR(10) NOT NULL UNIQUE,
    CurrencyName NVARCHAR(50) NOT NULL,
    ExchangeRate DECIMAL(18,6) DEFAULT 1,
    IsBaseCurrency BIT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    LastUpdated DATETIME DEFAULT GETDATE()
);

-- جدول دليل المحاسبة
CREATE TABLE AccountingGuide (
    AccountID INT IDENTITY(1,1) PRIMARY KEY,
    FormType NVARCHAR(50) NOT NULL,
    ExpenseType NVARCHAR(50) NOT NULL,
    Chapter NVARCHAR(50) NOT NULL,
    Article NVARCHAR(50) NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    TypeDetails NVARCHAR(100),
    Description NVARCHAR(255) NOT NULL,
    PreviousMonthExpenses DECIMAL(18,2) DEFAULT 0,
    CurrentMonthExpenses DECIMAL(18,2) DEFAULT 0,
    TotalExpenses DECIMAL(18,2) DEFAULT 0,
    AnnualAllocation DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الفترات المالية
CREATE TABLE FiscalPeriods (
    PeriodID INT IDENTITY(1,1) PRIMARY KEY,
    PeriodName NVARCHAR(50) NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    IsActive BIT DEFAULT 1,
    IsClosed BIT DEFAULT 0,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الرواتب الشهرية
CREATE TABLE MonthlySalaries (
    SalaryID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    PeriodID INT NOT NULL,
    BasicSalary DECIMAL(18,2) NOT NULL,
    TotalAllowances DECIMAL(18,2) DEFAULT 0,
    TotalDeductions DECIMAL(18,2) DEFAULT 0,
    NetSalary DECIMAL(18,2) NOT NULL,
    ProcessedDate DATETIME DEFAULT GETDATE(),
    ProcessedBy INT NOT NULL,
    
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (PeriodID) REFERENCES FiscalPeriods(PeriodID),
    FOREIGN KEY (ProcessedBy) REFERENCES Users(UserID)
);

-- جدول النسخ الاحتياطية
CREATE TABLE BackupHistory (
    BackupID INT IDENTITY(1,1) PRIMARY KEY,
    BackupPath NVARCHAR(255) NOT NULL,
    BackupSize BIGINT,
    BackupType NVARCHAR(50) NOT NULL, -- يدوي، تلقائي
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME DEFAULT GETDATE(),
    
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- إنشاء الفهارس
CREATE INDEX IX_Employees_EmployeeCode ON Employees(EmployeeCode);
CREATE INDEX IX_Employees_DivisionID ON Employees(DivisionID);
CREATE INDEX IX_MonthlySalaries_EmployeeID ON MonthlySalaries(EmployeeID);
CREATE INDEX IX_MonthlySalaries_PeriodID ON MonthlySalaries(PeriodID);

-- إدراج البيانات الأولية

-- إدراج المستخدمين الافتراضيين
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, Password, FullName, Role, IsActive)
    VALUES ('admin', 'admin123', 'مدير النظام', 'مدير', 1);
END

IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'accountant')
BEGIN
    INSERT INTO Users (Username, Password, FullName, Role, IsActive)
    VALUES ('accountant', 'acc123', 'المحاسب الرئيسي', 'محاسب', 1);
END

-- إدراج الدوائر الافتراضية
INSERT INTO Departments (DepartmentName, DepartmentCode, Description, IsActive)
VALUES
    ('الدائرة المالية', 'FIN', 'دائرة الشؤون المالية والمحاسبية', 1),
    ('دائرة الموارد البشرية', 'HR', 'دائرة إدارة الموارد البشرية', 1),
    ('الدائرة الإدارية', 'ADM', 'الدائرة الإدارية والخدمات', 1);

-- إدراج الأقسام الافتراضية
INSERT INTO Sections (SectionName, SectionCode, DepartmentID, Description, IsActive)
VALUES
    ('قسم المحاسبة', 'ACC', 1, 'قسم المحاسبة والحسابات', 1),
    ('قسم الرواتب', 'SAL', 1, 'قسم إدارة الرواتب والأجور', 1),
    ('قسم التوظيف', 'REC', 2, 'قسم التوظيف والاستقطاب', 1);

-- إدراج الشُعب الافتراضية
INSERT INTO Divisions (DivisionName, DivisionCode, SectionID, Description, IsActive)
VALUES
    ('شعبة الحسابات العامة', 'GAC', 1, 'شعبة إدارة الحسابات العامة', 1),
    ('شعبة حسابات الرواتب', 'SAC', 2, 'شعبة حسابات الرواتب والمخصصات', 1),
    ('شعبة التعيينات', 'APP', 3, 'شعبة التعيينات والترقيات', 1);

-- إدراج العناوين الوظيفية الافتراضية
INSERT INTO JobTitles (TitleName, TitleCode, Description, IsActive)
VALUES
    ('مدير عام', 'DG', 'منصب المدير العام', 1),
    ('مدير', 'MGR', 'منصب مدير', 1),
    ('محاسب أول', 'SA', 'محاسب أول', 1),
    ('محاسب', 'ACC', 'محاسب', 1),
    ('كاتب', 'CLK', 'كاتب', 1);

-- إدراج الدرجات الوظيفية الافتراضية
INSERT INTO JobGrades (GradeName, GradeLevel, BasicSalary, IsActive)
VALUES
    ('خاصة', 1, 2500000, 1),
    ('الأولى', 2, 2000000, 1),
    ('الثانية', 3, 1800000, 1),
    ('الثالثة', 4, 1600000, 1),
    ('الرابعة', 5, 1400000, 1),
    ('الخامسة', 6, 1200000, 1),
    ('السادسة', 7, 1000000, 1),
    ('السابعة', 8, 900000, 1),
    ('الثامنة', 9, 800000, 1),
    ('التاسعة', 10, 700000, 1),
    ('العاشرة', 11, 600000, 1);

-- إدراج الشهادات الافتراضية
INSERT INTO Qualifications (QualificationName, QualificationCode, AllowanceAmount, IsActive)
VALUES
    ('دكتوراه', 'PHD', 300000, 1),
    ('ماجستير', 'MSC', 200000, 1),
    ('بكالوريوس', 'BSC', 100000, 1),
    ('دبلوم', 'DIP', 50000, 1),
    ('إعدادية', 'SEC', 0, 1);

-- إدراج العملات الافتراضية
INSERT INTO Currencies (CurrencyCode, CurrencyName, ExchangeRate, IsBaseCurrency, IsActive)
VALUES
    ('IQD', 'دينار عراقي', 1, 1, 1),
    ('USD', 'دولار أمريكي', 1320, 0, 1);

-- إدراج البنوك الافتراضية
INSERT INTO Banks (BankName, BranchName, BranchCode, AccountNumber, AccountType, IsActive)
VALUES
    ('المصرف العراقي للتجارة', 'فرع بغداد', 'TBI001', '**********', 'رواتب', 1),
    ('مصرف الرافدين', 'فرع الكرخ', 'RAF001', '**********', 'تشغيلي', 1);

PRINT 'تم إنشاء قاعدة البيانات والبيانات الأولية بنجاح';
GO
