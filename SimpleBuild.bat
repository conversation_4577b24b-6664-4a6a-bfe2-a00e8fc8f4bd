@echo off
echo Building project...
echo.

cd /d "%~dp0"

REM Try to build using .NET Framework MSBuild
if exist "C:\Windows\Microsoft.NET\Framework\v4.0.30319\MSBuild.exe" (
    echo Using .NET Framework MSBuild...
    "C:\Windows\Microsoft.NET\Framework\v4.0.30319\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
) else (
    echo Trying generic MSBuild...
    msbuild UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
)

if %errorlevel% equ 0 (
    echo.
    echo Build successful!
    echo.
    
    if exist "bin\Debug\UnifiedAccountingSystem.exe" (
        echo Running application...
        start "" "bin\Debug\UnifiedAccountingSystem.exe"
        echo Application started!
    ) else (
        echo Executable not found
    )
) else (
    echo.
    echo Build failed!
    echo Please open project in Visual Studio and run directly
)

pause
