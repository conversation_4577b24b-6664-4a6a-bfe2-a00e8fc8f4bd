Imports System.Data.SqlClient
Imports System.Configuration

''' <summary>
''' فئة إدارة الاتصال بقاعدة البيانات
''' </summary>
Public Class DatabaseConnection
    Private Shared _connectionString As String = ""

    ''' <summary>
    ''' الحصول على نص الاتصال بقاعدة البيانات
    ''' </summary>
    Public Shared ReadOnly Property ConnectionString As String
        Get
            If String.IsNullOrEmpty(_connectionString) Then
                _connectionString = ConfigurationManager.ConnectionStrings("UnifiedAccountingDB").ConnectionString
            End If
            Return _connectionString
        End Get
    End Property

    ''' <summary>
    ''' إنشاء اتصال جديد بقاعدة البيانات
    ''' </summary>
    Public Shared Function CreateConnection() As SqlConnection
        Return New SqlConnection(ConnectionString)
    End Function

    ''' <summary>
    ''' اختبار الاتصال بقاعدة البيانات
    ''' </summary>
    Public Shared Function TestConnection() As Boolean
        Try
            Using connection As SqlConnection = CreateConnection()
                connection.Open()
                Return True
            End Using
        Catch ex As Exception
            Return False
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ استعلام وإرجاع DataTable
    ''' </summary>
    Public Shared Function ExecuteQuery(query As String, Optional parameters As SqlParameter() = Nothing) As DataTable
        Dim dataTable As New DataTable()
        
        Try
            Using connection As SqlConnection = CreateConnection()
                Using command As New SqlCommand(query, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    Using adapter As New SqlDataAdapter(command)
                        adapter.Fill(dataTable)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("خطأ في تنفيذ الاستعلام: " & ex.Message)
        End Try

        Return dataTable
    End Function

    ''' <summary>
    ''' تنفيذ أمر غير استعلام (INSERT, UPDATE, DELETE)
    ''' </summary>
    Public Shared Function ExecuteNonQuery(query As String, Optional parameters As SqlParameter() = Nothing) As Integer
        Dim rowsAffected As Integer = 0

        Try
            Using connection As SqlConnection = CreateConnection()
                Using command As New SqlCommand(query, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If

                    connection.Open()
                    rowsAffected = command.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("خطأ في تنفيذ الأمر: " & ex.Message)
        End Try

        Return rowsAffected
    End Function

    ''' <summary>
    ''' تنفيذ استعلام وإرجاع قيمة واحدة
    ''' </summary>
    Public Shared Function ExecuteScalar(query As String, Optional parameters As SqlParameter() = Nothing) As Object
        Dim result As Object = Nothing

        Try
            Using connection As SqlConnection = CreateConnection()
                Using command As New SqlCommand(query, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If

                    connection.Open()
                    result = command.ExecuteScalar()
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("خطأ في تنفيذ الاستعلام: " & ex.Message)
        End Try

        Return result
    End Function

    ''' <summary>
    ''' إنشاء قاعدة البيانات إذا لم تكن موجودة
    ''' </summary>
    Public Shared Function CreateDatabaseIfNotExists() As Boolean
        Try
            ' التحقق من وجود قاعدة البيانات أولاً
            If TestConnection() Then
                Return True
            End If

            ' قراءة سكريبت إنشاء قاعدة البيانات المحلية
            Dim createScript As String = IO.File.ReadAllText("Database\CreateLocalDB.sql")

            ' تنفيذ سكريبت إنشاء قاعدة البيانات
            ExecuteScript(createScript)

            Return True
        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء قاعدة البيانات: " & ex.Message)
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ سكريبت SQL
    ''' </summary>
    Private Shared Sub ExecuteScript(script As String)
        Dim commands() As String = script.Split(New String() {"GO"}, StringSplitOptions.RemoveEmptyEntries)

        Using connection As SqlConnection = CreateConnection()
            connection.Open()

            For Each commandText As String In commands
                If Not String.IsNullOrWhiteSpace(commandText) Then
                    Using command As New SqlCommand(commandText.Trim(), connection)
                        command.ExecuteNonQuery()
                    End Using
                End If
            Next
        End Using
    End Sub

    ''' <summary>
    ''' إنشاء نسخة احتياطية من قاعدة البيانات
    ''' </summary>
    Public Shared Function BackupDatabase(backupPath As String) As Boolean
        Try
            Dim query As String = "BACKUP DATABASE [UnifiedAccountingDB] TO DISK = '" & backupPath & "'"
            ExecuteNonQuery(query)
            Return True
        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء النسخة الاحتياطية: " & ex.Message)
        End Try
    End Function

    ''' <summary>
    ''' استعادة قاعدة البيانات من نسخة احتياطية
    ''' </summary>
    Public Shared Function RestoreDatabase(backupPath As String) As Boolean
        Try
            Dim query As String = "RESTORE DATABASE [UnifiedAccountingDB] FROM DISK = '" & backupPath & "' WITH REPLACE"
            ExecuteNonQuery(query)
            Return True
        Catch ex As Exception
            Throw New Exception("خطأ في استعادة النسخة الاحتياطية: " & ex.Message)
        End Try
    End Function
End Class
