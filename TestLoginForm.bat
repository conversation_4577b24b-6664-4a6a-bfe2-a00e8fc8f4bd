@echo off
echo تشغيل شاشة تسجيل الدخول المحدثة...
echo ================================

cd /d "%~dp0"

echo بناء المشروع...
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug

if %errorlevel% neq 0 (
    echo فشل في بناء المشروع!
    pause
    exit /b 1
)

echo تشغيل التطبيق...
start "" "bin\Debug\UnifiedAccountingSystem.exe"

echo تم تشغيل التطبيق بنجاح!
echo يجب أن تظهر شاشة تسجيل الدخول في وسط الشاشة بحجم مصغر
pause
