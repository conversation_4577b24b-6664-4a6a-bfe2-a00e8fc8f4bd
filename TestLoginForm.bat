@echo off
echo تشغيل شاشة تسجيل الدخول المحدثة والموسطة...
echo ===============================================

cd /d "%~dp0"

echo معلومات الشاشة:
echo - الحجم الجديد: 450x320 بكسل
echo - الموضع: وسط الشاشة تلقائياً
echo - التحسينات: توسيط ديناميكي وتأثيرات بصرية

echo.
echo بناء المشروع...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug
) else (
    echo لم يتم العثور على MSBuild، جاري المحاولة مع dotnet...
    dotnet build UnifiedAccountingSystem.sln --configuration Debug
)

if %errorlevel% neq 0 (
    echo فشل في بناء المشروع!
    echo يرجى فتح المشروع في Visual Studio وتشغيله مباشرة
    pause
    exit /b 1
)

echo.
echo تشغيل التطبيق...
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    start "" "bin\Debug\UnifiedAccountingSystem.exe"
    echo تم تشغيل التطبيق بنجاح!
    echo شاشة تسجيل الدخول ستظهر في وسط الشاشة
) else (
    echo لم يتم العثور على الملف التنفيذي
    echo يرجى تشغيل المشروع من Visual Studio
)

pause
