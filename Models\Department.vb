''' <summary>
''' نموذج بيانات الدائرة
''' </summary>
Public Class Department
    Public Property DepartmentID As Integer
    Public Property DepartmentName As String
    Public Property DepartmentCode As String
    Public Property Description As String
    Public Property IsActive As Boolean
    Public Property CreatedDate As DateTime
    
    Public Sub New()
        DepartmentID = 0
        DepartmentName = ""
        DepartmentCode = ""
        Description = ""
        IsActive = True
        CreatedDate = DateTime.Now
    End Sub
End Class

''' <summary>
''' نموذج بيانات القسم
''' </summary>
Public Class Section
    Public Property SectionID As Integer
    Public Property SectionName As String
    Public Property SectionCode As String
    Public Property DepartmentID As Integer
    Public Property DepartmentName As String
    Public Property Description As String
    Public Property IsActive As Boolean
    Public Property CreatedDate As DateTime
    
    Public Sub New()
        SectionID = 0
        SectionName = ""
        SectionCode = ""
        DepartmentID = 0
        DepartmentName = ""
        Description = ""
        IsActive = True
        CreatedDate = DateTime.Now
    End Sub
End Class

''' <summary>
''' نموذج بيانات الشعبة
''' </summary>
Public Class Division
    Public Property DivisionID As Integer
    Public Property DivisionName As String
    Public Property DivisionCode As String
    Public Property SectionID As Integer
    Public Property SectionName As String
    Public Property DepartmentName As String
    Public Property Description As String
    Public Property IsActive As Boolean
    Public Property CreatedDate As DateTime
    
    Public Sub New()
        DivisionID = 0
        DivisionName = ""
        DivisionCode = ""
        SectionID = 0
        SectionName = ""
        DepartmentName = ""
        Description = ""
        IsActive = True
        CreatedDate = DateTime.Now
    End Sub
End Class

''' <summary>
''' نموذج بيانات العنوان الوظيفي
''' </summary>
Public Class JobTitle
    Public Property JobTitleID As Integer
    Public Property TitleName As String
    Public Property TitleCode As String
    Public Property Description As String
    Public Property IsActive As Boolean
    
    Public Sub New()
        JobTitleID = 0
        TitleName = ""
        TitleCode = ""
        Description = ""
        IsActive = True
    End Sub
End Class

''' <summary>
''' نموذج بيانات الدرجة الوظيفية
''' </summary>
Public Class JobGrade
    Public Property GradeID As Integer
    Public Property GradeName As String
    Public Property GradeLevel As Integer
    Public Property BasicSalary As Decimal
    Public Property IsActive As Boolean
    
    Public Sub New()
        GradeID = 0
        GradeName = ""
        GradeLevel = 0
        BasicSalary = 0
        IsActive = True
    End Sub
End Class

''' <summary>
''' نموذج بيانات الشهادة
''' </summary>
Public Class Qualification
    Public Property QualificationID As Integer
    Public Property QualificationName As String
    Public Property QualificationCode As String
    Public Property AllowanceAmount As Decimal
    Public Property IsActive As Boolean
    
    Public Sub New()
        QualificationID = 0
        QualificationName = ""
        QualificationCode = ""
        AllowanceAmount = 0
        IsActive = True
    End Sub
End Class
