@echo off
chcp 65001 >nul
echo ========================================
echo فحص النظام المحاسبي الموحد
echo وزارة الشباب والرياضة
echo ========================================
echo.

set "ERRORS=0"

echo جاري فحص متطلبات النظام...
echo.

REM فحص .NET Framework
echo [1/6] فحص .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET Framework 4.5 غير مثبت
    set /a ERRORS+=1
) else (
    echo ✓ .NET Framework متوفر
)

REM فحص LocalDB
echo [2/6] فحص LocalDB...
sqllocaldb info MSSQLLocalDB >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ LocalDB غير متوفر
    echo    يرجى تشغيل SetupLocalDB.bat لإعداد قاعدة البيانات
    set /a ERRORS+=1
) else (
    echo ✓ LocalDB متوفر
)

REM فحص الاتصال بقاعدة البيانات
echo [3/6] فحص الاتصال بقاعدة البيانات...
sqlcmd -S "(LocalDB)\MSSQLLocalDB" -Q "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ لا يمكن الاتصال بـ LocalDB
    set /a ERRORS+=1
) else (
    echo ✓ الاتصال بـ LocalDB يعمل
)

REM فحص وجود قاعدة البيانات
echo [4/6] فحص قاعدة البيانات...
if exist "App_Data\UnifiedAccountingDB.mdf" (
    echo ✓ قاعدة البيانات UnifiedAccountingDB موجودة
) else (
    echo ❌ قاعدة البيانات UnifiedAccountingDB غير موجودة
    echo    يرجى تشغيل SetupLocalDB.bat لإنشاء قاعدة البيانات
    set /a ERRORS+=1
)

REM فحص ملفات النظام
echo [5/6] فحص ملفات النظام...
set "APP_FOUND=0"
if exist "UnifiedAccountingSystem.exe" (
    set "APP_FOUND=1"
    set "APP_PATH=UnifiedAccountingSystem.exe"
)
if exist "bin\Debug\UnifiedAccountingSystem.exe" (
    set "APP_FOUND=1"
    set "APP_PATH=bin\Debug\UnifiedAccountingSystem.exe"
)
if exist "bin\Release\UnifiedAccountingSystem.exe" (
    set "APP_FOUND=1"
    set "APP_PATH=bin\Release\UnifiedAccountingSystem.exe"
)

if %APP_FOUND%==0 (
    echo ❌ ملف التطبيق غير موجود
    echo    يرجى بناء المشروع في Visual Studio
    set /a ERRORS+=1
) else (
    echo ✓ ملف التطبيق موجود: %APP_PATH%
)

REM فحص ملفات الإعداد
echo [6/6] فحص ملفات الإعداد...
if not exist "App.config" (
    echo ❌ ملف App.config غير موجود
    set /a ERRORS+=1
) else (
    echo ✓ ملف App.config موجود
)

echo.
echo ========================================

if %ERRORS%==0 (
    echo ✅ جميع الفحوصات نجحت!
    echo النظام جاهز للتشغيل
    echo.
    echo لتشغيل النظام:
    echo - اضغط على RunSystem.bat
    echo - أو افتح المشروع في Visual Studio واضغط F5
    echo.
    echo بيانات الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
) else (
    echo ❌ تم العثور على %ERRORS% مشكلة/مشاكل
    echo يرجى حل المشاكل المذكورة أعلاه قبل تشغيل النظام
    echo.
    echo للمساعدة:
    echo - راجع ملف README.md
    echo - راجع ملف تعليمات_التشغيل.md
    echo - تشغيل SetupDatabase.bat لإعداد قاعدة البيانات
)

echo.
echo تفاصيل النظام:
echo - اسم النظام: النظام المحاسبي الموحد
echo - المؤسسة: وزارة الشباب والرياضة
echo - الإصدار: 1.0.0
echo - تاريخ الفحص: %date% %time%
echo.

REM عرض معلومات إضافية إذا كان النظام جاهز
if %ERRORS%==0 (
    echo معلومات إضافية:
    if exist "%APP_PATH%" (
        echo - حجم ملف التطبيق: 
        for %%A in ("%APP_PATH%") do echo   %%~zA بايت
        echo - تاريخ آخر تعديل: 
        for %%A in ("%APP_PATH%") do echo   %%~tA
    )
    
    REM عرض عدد الجداول في قاعدة البيانات
    echo - عدد جداول قاعدة البيانات:
    sqlcmd -S .\SQLEXPRESS -E -d UnifiedAccountingDB -Q "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -h -1 2>nul | findstr /r "^[0-9]"
)

echo ========================================
echo اضغط أي مفتاح للإغلاق...
pause >nul
