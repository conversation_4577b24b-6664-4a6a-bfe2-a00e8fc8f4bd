Imports System.Data.SqlClient

''' <summary>
''' طبقة الوصول للبيانات - الموظفين
''' </summary>
Public Class EmployeeDAL
    
    ''' <summary>
    ''' الحصول على جميع الموظفين
    ''' </summary>
    Public Shared Function GetAllEmployees() As List(Of Employee)
        Dim employees As New List(Of Employee)
        
        Dim query As String = "
            SELECT e.*, jt.TitleName, jg.GradeName, q.QualificationName,
                   d.DivisionName, s.SectionName, dep.DepartmentName
            FROM Employees e
            INNER JOIN JobTitles jt ON e.JobTitleID = jt.JobTitleID
            INNER JOIN JobGrades jg ON e.GradeID = jg.GradeID
            LEFT JOIN Qualifications q ON e.QualificationID = q.QualificationID
            INNER JOIN Divisions d ON e.DivisionID = d.DivisionID
            INNER JOIN Sections s ON d.SectionID = s.SectionID
            INNER JOIN Departments dep ON s.DepartmentID = dep.DepartmentID
            WHERE e.IsActive = 1
            ORDER BY e.FullName"
        
        Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query)
        
        For Each row As DataRow In dataTable.Rows
            employees.Add(MapDataRowToEmployee(row))
        Next
        
        Return employees
    End Function
    
    ''' <summary>
    ''' الحصول على موظف بالمعرف
    ''' </summary>
    Public Shared Function GetEmployeeById(employeeId As Integer) As Employee
        Dim query As String = "
            SELECT e.*, jt.TitleName, jg.GradeName, q.QualificationName,
                   d.DivisionName, s.SectionName, dep.DepartmentName
            FROM Employees e
            INNER JOIN JobTitles jt ON e.JobTitleID = jt.JobTitleID
            INNER JOIN JobGrades jg ON e.GradeID = jg.GradeID
            LEFT JOIN Qualifications q ON e.QualificationID = q.QualificationID
            INNER JOIN Divisions d ON e.DivisionID = d.DivisionID
            INNER JOIN Sections s ON d.SectionID = s.SectionID
            INNER JOIN Departments dep ON s.DepartmentID = dep.DepartmentID
            WHERE e.EmployeeID = @EmployeeID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@EmployeeID", employeeId)
        }
        
        Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
        
        If dataTable.Rows.Count > 0 Then
            Return MapDataRowToEmployee(dataTable.Rows(0))
        Else
            Return Nothing
        End If
    End Function
    
    ''' <summary>
    ''' البحث عن الموظفين
    ''' </summary>
    Public Shared Function SearchEmployees(searchText As String) As List(Of Employee)
        Dim employees As New List(Of Employee)
        
        Dim query As String = "
            SELECT e.*, jt.TitleName, jg.GradeName, q.QualificationName,
                   d.DivisionName, s.SectionName, dep.DepartmentName
            FROM Employees e
            INNER JOIN JobTitles jt ON e.JobTitleID = jt.JobTitleID
            INNER JOIN JobGrades jg ON e.GradeID = jg.GradeID
            LEFT JOIN Qualifications q ON e.QualificationID = q.QualificationID
            INNER JOIN Divisions d ON e.DivisionID = d.DivisionID
            INNER JOIN Sections s ON d.SectionID = s.SectionID
            INNER JOIN Departments dep ON s.DepartmentID = dep.DepartmentID
            WHERE e.IsActive = 1 AND (
                e.FullName LIKE @SearchText OR 
                e.EmployeeCode LIKE @SearchText OR
                jt.TitleName LIKE @SearchText OR
                d.DivisionName LIKE @SearchText
            )
            ORDER BY e.FullName"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@SearchText", "%" & searchText & "%")
        }
        
        Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
        
        For Each row As DataRow In dataTable.Rows
            employees.Add(MapDataRowToEmployee(row))
        Next
        
        Return employees
    End Function
    
    ''' <summary>
    ''' إضافة موظف جديد
    ''' </summary>
    Public Shared Function AddEmployee(employee As Employee) As Integer
        Dim query As String = "
            INSERT INTO Employees (
                EmployeeCode, FullName, JobTitleID, GradeID, QualificationID, 
                DivisionID, Stage, NewSalary, PositionAllowance, MaritalAllowance, 
                ChildrenAllowance, EngineeringAllowance, QualificationAllowance, 
                CraftAllowance, DangerAllowance, TransportAllowance, UniversityAllowance,
                RetirementDeduction, GovernmentContribution, IncomeTax, SocialProtection,
                InsuranceInstallments, ExecutionCircles, HealthDeposits, 
                BankReservations, ExecutionReservations, HireDate, IsActive
            ) VALUES (
                @EmployeeCode, @FullName, @JobTitleID, @GradeID, @QualificationID,
                @DivisionID, @Stage, @NewSalary, @PositionAllowance, @MaritalAllowance,
                @ChildrenAllowance, @EngineeringAllowance, @QualificationAllowance,
                @CraftAllowance, @DangerAllowance, @TransportAllowance, @UniversityAllowance,
                @RetirementDeduction, @GovernmentContribution, @IncomeTax, @SocialProtection,
                @InsuranceInstallments, @ExecutionCircles, @HealthDeposits,
                @BankReservations, @ExecutionReservations, @HireDate, @IsActive
            );
            SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = CreateEmployeeParameters(employee)
        
        Dim result As Object = DatabaseConnection.ExecuteScalar(query, parameters)
        Return Convert.ToInt32(result)
    End Function
    
    ''' <summary>
    ''' تحديث بيانات موظف
    ''' </summary>
    Public Shared Function UpdateEmployee(employee As Employee) As Boolean
        Dim query As String = "
            UPDATE Employees SET
                EmployeeCode = @EmployeeCode, FullName = @FullName, 
                JobTitleID = @JobTitleID, GradeID = @GradeID, 
                QualificationID = @QualificationID, DivisionID = @DivisionID,
                Stage = @Stage, NewSalary = @NewSalary,
                PositionAllowance = @PositionAllowance, MaritalAllowance = @MaritalAllowance,
                ChildrenAllowance = @ChildrenAllowance, EngineeringAllowance = @EngineeringAllowance,
                QualificationAllowance = @QualificationAllowance, CraftAllowance = @CraftAllowance,
                DangerAllowance = @DangerAllowance, TransportAllowance = @TransportAllowance,
                UniversityAllowance = @UniversityAllowance, RetirementDeduction = @RetirementDeduction,
                GovernmentContribution = @GovernmentContribution, IncomeTax = @IncomeTax,
                SocialProtection = @SocialProtection, InsuranceInstallments = @InsuranceInstallments,
                ExecutionCircles = @ExecutionCircles, HealthDeposits = @HealthDeposits,
                BankReservations = @BankReservations, ExecutionReservations = @ExecutionReservations,
                HireDate = @HireDate, IsActive = @IsActive
            WHERE EmployeeID = @EmployeeID"
        
        Dim parameters() As SqlParameter = CreateEmployeeParameters(employee)
        ReDim Preserve parameters(parameters.Length)
        parameters(parameters.Length - 1) = New SqlParameter("@EmployeeID", employee.EmployeeID)
        
        Dim rowsAffected As Integer = DatabaseConnection.ExecuteNonQuery(query, parameters)
        Return rowsAffected > 0
    End Function
    
    ''' <summary>
    ''' حذف موظف (حذف منطقي)
    ''' </summary>
    Public Shared Function DeleteEmployee(employeeId As Integer) As Boolean
        Dim query As String = "UPDATE Employees SET IsActive = 0 WHERE EmployeeID = @EmployeeID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@EmployeeID", employeeId)
        }
        
        Dim rowsAffected As Integer = DatabaseConnection.ExecuteNonQuery(query, parameters)
        Return rowsAffected > 0
    End Function
    
    ''' <summary>
    ''' تحويل صف البيانات إلى كائن موظف
    ''' </summary>
    Private Shared Function MapDataRowToEmployee(row As DataRow) As Employee
        Dim employee As New Employee()
        
        employee.EmployeeID = Convert.ToInt32(row("EmployeeID"))
        employee.EmployeeCode = row("EmployeeCode").ToString()
        employee.FullName = row("FullName").ToString()
        employee.JobTitleID = Convert.ToInt32(row("JobTitleID"))
        employee.JobTitleName = row("TitleName").ToString()
        employee.GradeID = Convert.ToInt32(row("GradeID"))
        employee.GradeName = row("GradeName").ToString()
        
        If Not IsDBNull(row("QualificationID")) Then
            employee.QualificationID = Convert.ToInt32(row("QualificationID"))
            employee.QualificationName = row("QualificationName").ToString()
        End If
        
        employee.DivisionID = Convert.ToInt32(row("DivisionID"))
        employee.DivisionName = row("DivisionName").ToString()
        employee.SectionName = row("SectionName").ToString()
        employee.DepartmentName = row("DepartmentName").ToString()
        employee.Stage = row("Stage").ToString()
        employee.NewSalary = Convert.ToDecimal(row("NewSalary"))
        
        ' المخصصات
        employee.PositionAllowance = Convert.ToDecimal(row("PositionAllowance"))
        employee.MaritalAllowance = Convert.ToDecimal(row("MaritalAllowance"))
        employee.ChildrenAllowance = Convert.ToDecimal(row("ChildrenAllowance"))
        employee.EngineeringAllowance = Convert.ToDecimal(row("EngineeringAllowance"))
        employee.QualificationAllowance = Convert.ToDecimal(row("QualificationAllowance"))
        employee.CraftAllowance = Convert.ToDecimal(row("CraftAllowance"))
        employee.DangerAllowance = Convert.ToDecimal(row("DangerAllowance"))
        employee.TransportAllowance = Convert.ToDecimal(row("TransportAllowance"))
        employee.UniversityAllowance = Convert.ToDecimal(row("UniversityAllowance"))
        
        ' الاستقطاعات
        employee.RetirementDeduction = Convert.ToDecimal(row("RetirementDeduction"))
        employee.GovernmentContribution = Convert.ToDecimal(row("GovernmentContribution"))
        employee.IncomeTax = Convert.ToDecimal(row("IncomeTax"))
        employee.SocialProtection = Convert.ToDecimal(row("SocialProtection"))
        employee.InsuranceInstallments = Convert.ToDecimal(row("InsuranceInstallments"))
        employee.ExecutionCircles = Convert.ToDecimal(row("ExecutionCircles"))
        employee.HealthDeposits = Convert.ToDecimal(row("HealthDeposits"))
        employee.BankReservations = Convert.ToDecimal(row("BankReservations"))
        employee.ExecutionReservations = Convert.ToDecimal(row("ExecutionReservations"))
        
        employee.HireDate = Convert.ToDateTime(row("HireDate"))
        employee.IsActive = Convert.ToBoolean(row("IsActive"))
        employee.CreatedDate = Convert.ToDateTime(row("CreatedDate"))
        
        Return employee
    End Function
    
    ''' <summary>
    ''' إنشاء معاملات SQL للموظف
    ''' </summary>
    Private Shared Function CreateEmployeeParameters(employee As Employee) As SqlParameter()
        Return {
            New SqlParameter("@EmployeeCode", employee.EmployeeCode),
            New SqlParameter("@FullName", employee.FullName),
            New SqlParameter("@JobTitleID", employee.JobTitleID),
            New SqlParameter("@GradeID", employee.GradeID),
            New SqlParameter("@QualificationID", If(employee.QualificationID.HasValue, employee.QualificationID.Value, DBNull.Value)),
            New SqlParameter("@DivisionID", employee.DivisionID),
            New SqlParameter("@Stage", employee.Stage),
            New SqlParameter("@NewSalary", employee.NewSalary),
            New SqlParameter("@PositionAllowance", employee.PositionAllowance),
            New SqlParameter("@MaritalAllowance", employee.MaritalAllowance),
            New SqlParameter("@ChildrenAllowance", employee.ChildrenAllowance),
            New SqlParameter("@EngineeringAllowance", employee.EngineeringAllowance),
            New SqlParameter("@QualificationAllowance", employee.QualificationAllowance),
            New SqlParameter("@CraftAllowance", employee.CraftAllowance),
            New SqlParameter("@DangerAllowance", employee.DangerAllowance),
            New SqlParameter("@TransportAllowance", employee.TransportAllowance),
            New SqlParameter("@UniversityAllowance", employee.UniversityAllowance),
            New SqlParameter("@RetirementDeduction", employee.RetirementDeduction),
            New SqlParameter("@GovernmentContribution", employee.GovernmentContribution),
            New SqlParameter("@IncomeTax", employee.IncomeTax),
            New SqlParameter("@SocialProtection", employee.SocialProtection),
            New SqlParameter("@InsuranceInstallments", employee.InsuranceInstallments),
            New SqlParameter("@ExecutionCircles", employee.ExecutionCircles),
            New SqlParameter("@HealthDeposits", employee.HealthDeposits),
            New SqlParameter("@BankReservations", employee.BankReservations),
            New SqlParameter("@ExecutionReservations", employee.ExecutionReservations),
            New SqlParameter("@HireDate", employee.HireDate),
            New SqlParameter("@IsActive", employee.IsActive)
        }
    End Function
End Class
