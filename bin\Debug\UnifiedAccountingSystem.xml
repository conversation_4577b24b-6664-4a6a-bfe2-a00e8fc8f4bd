﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
UnifiedAccountingSystem
</name>
</assembly>
<members>
<member name="P:UnifiedAccountingSystem.My.Resources.Resources.ResourceManager">
	<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member><member name="P:UnifiedAccountingSystem.My.Resources.Resources.Culture">
	<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member><member name="T:UnifiedAccountingSystem.My.Resources.Resources">
	<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member><member name="P:UnifiedAccountingSystem.MonthlySalary.GrossSalary">
	<summary>
 حساب إجمالي الراتب
 </summary>
</member><member name="T:UnifiedAccountingSystem.MonthlySalary">
	<summary>
 نموذج بيانات الراتب الشهري
 </summary>
</member><member name="T:UnifiedAccountingSystem.Bank">
	<summary>
 نموذج بيانات البنك
 </summary>
</member><member name="T:UnifiedAccountingSystem.Currency">
	<summary>
 نموذج بيانات العملة
 </summary>
</member><member name="P:UnifiedAccountingSystem.AccountingGuide.RemainingBalance">
	<summary>
 حساب الرصيد المتبقي
 </summary>
</member><member name="P:UnifiedAccountingSystem.AccountingGuide.SpendingPercentage">
	<summary>
 حساب نسبة الإنفاق
 </summary>
</member><member name="T:UnifiedAccountingSystem.AccountingGuide">
	<summary>
 نموذج بيانات دليل المحاسبة
 </summary>
</member><member name="P:UnifiedAccountingSystem.FiscalPeriod.IsValidPeriod">
	<summary>
 التحقق من صحة الفترة
 </summary>
</member><member name="P:UnifiedAccountingSystem.FiscalPeriod.IsCurrentPeriod">
	<summary>
 التحقق من كون الفترة حالية
 </summary>
</member><member name="T:UnifiedAccountingSystem.FiscalPeriod">
	<summary>
 نموذج بيانات الفترة المالية
 </summary>
</member><member name="P:UnifiedAccountingSystem.Employee.TotalAllowances">
	<summary>
 حساب إجمالي المخصصات
 </summary>
</member><member name="P:UnifiedAccountingSystem.Employee.TotalDeductions">
	<summary>
 حساب إجمالي الاستقطاعات
 </summary>
</member><member name="P:UnifiedAccountingSystem.Employee.GrossSalary">
	<summary>
 حساب إجمالي الراتب
 </summary>
</member><member name="P:UnifiedAccountingSystem.Employee.NetSalary">
	<summary>
 حساب صافي الراتب
 </summary>
</member><member name="M:UnifiedAccountingSystem.Employee.#ctor">
	<summary>
 منشئ افتراضي
 </summary>
</member><member name="M:UnifiedAccountingSystem.Employee.CalculateRetirementDeduction">
	<summary>
 حساب استقطاع التقاعد (10%)
 </summary>
</member><member name="M:UnifiedAccountingSystem.Employee.CalculateGovernmentContribution">
	<summary>
 حساب المساهمة الحكومية (15%)
 </summary>
</member><member name="M:UnifiedAccountingSystem.Employee.CalculateIncomeTax">
	<summary>
 حساب ضريبة الدخل حسب الشرائح
 </summary>
</member><member name="M:UnifiedAccountingSystem.Employee.UpdateCalculations">
	<summary>
 تحديث جميع الحسابات التلقائية
 </summary>
</member><member name="T:UnifiedAccountingSystem.Employee">
	<summary>
 نموذج بيانات الموظف
 </summary>
</member><member name="T:UnifiedAccountingSystem.Department">
	<summary>
 نموذج بيانات الدائرة
 </summary>
</member><member name="T:UnifiedAccountingSystem.Section">
	<summary>
 نموذج بيانات القسم
 </summary>
</member><member name="T:UnifiedAccountingSystem.Division">
	<summary>
 نموذج بيانات الشعبة
 </summary>
</member><member name="T:UnifiedAccountingSystem.JobTitle">
	<summary>
 نموذج بيانات العنوان الوظيفي
 </summary>
</member><member name="T:UnifiedAccountingSystem.JobGrade">
	<summary>
 نموذج بيانات الدرجة الوظيفية
 </summary>
</member><member name="T:UnifiedAccountingSystem.Qualification">
	<summary>
 نموذج بيانات الشهادة
 </summary>
</member><member name="M:UnifiedAccountingSystem.UnifiedAccountingSystem.MainForm.InitializeForm">
	<summary>
 تهيئة الشاشة الرئيسية
 </summary>
</member><member name="M:UnifiedAccountingSystem.UnifiedAccountingSystem.MainForm.ApplyFontToControls(System.Windows.Forms.Control,System.Drawing.Font)">
	<summary>
 تطبيق الخط على جميع العناصر
 </summary>
</member><member name="M:UnifiedAccountingSystem.UnifiedAccountingSystem.MainForm.SetupMenus">
	<summary>
 إعداد القوائم الرئيسية
 </summary>
</member><member name="M:UnifiedAccountingSystem.UnifiedAccountingSystem.MainForm.CreateMenuItem(System.String,System.EventHandler)">
	<summary>
 إنشاء عنصر قائمة
 </summary>
</member><member name="M:UnifiedAccountingSystem.UnifiedAccountingSystem.MainForm.DisplayWelcomeMessage">
	<summary>
 عرض رسالة الترحيب
 </summary>
</member><member name="T:UnifiedAccountingSystem.UnifiedAccountingSystem.MainForm">
	<summary>
 الشاشة الرئيسية للنظام المحاسبي الموحد
 </summary>
</member><member name="T:UnifiedAccountingSystem.UnifiedAccountingSystem.DevExpressLoginForm">
	<summary>
 شاشة تسجيل الدخول باستخدام DevExpress
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.LoginForm_Shown(System.Object,System.EventArgs)">
	<summary>
 معالج حدث إظهار الشاشة
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.LoginForm_Activated(System.Object,System.EventArgs)">
	<summary>
 معالج حدث تفعيل الشاشة
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.InitializeForm">
	<summary>
 تهيئة عناصر الشاشة
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.GetArabicFont">
	<summary>
 الحصول على الخط العربي المناسب
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.ApplyFontToControls(System.Windows.Forms.Control,System.Drawing.Font)">
	<summary>
 تطبيق الخط على جميع العناصر
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.CenterFormOnScreen">
	<summary>
 توسيط الشاشة في وسط الشاشة
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.EnsureControlsVisible">
	<summary>
 التأكد من ظهور جميع العناصر
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.MakeChildControlsVisible(System.Windows.Forms.Control)">
	<summary>
 جعل العناصر الفرعية مرئية
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.AddVisualEffects">
	<summary>
 إضافة تأثيرات بصرية للشاشة
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.TestDatabaseConnection">
	<summary>
 اختبار الاتصال بقاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.Button_MouseEnter(System.Object,System.EventArgs)">
	<summary>
 تأثير عند مرور الماوس على الزر
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.Button_MouseLeave(System.Object,System.EventArgs)">
	<summary>
 تأثير عند مغادرة الماوس للزر
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.TextBox_Enter(System.Object,System.EventArgs)">
	<summary>
 تأثير عند دخول التركيز للحقل النصي
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.TextBox_Leave(System.Object,System.EventArgs)">
	<summary>
 تأثير عند مغادرة التركيز للحقل النصي
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.btnLogin_Click(System.Object,System.EventArgs)">
	<summary>
 معالج حدث النقر على زر تسجيل الدخول
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.ValidateInput">
	<summary>
 التحقق من صحة البيانات المدخلة
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.PerformLogin">
	<summary>
 تنفيذ عملية تسجيل الدخول
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.AuthenticateUser(System.String,System.String)">
	<summary>
 التحقق من صحة بيانات المستخدم
 </summary>
	<param name="username">اسم المستخدم</param>
	<param name="password">كلمة المرور</param>
	<returns>بيانات المستخدم إذا كانت صحيحة، Nothing إذا كانت خاطئة</returns>
</member><member name="M:UnifiedAccountingSystem.LoginForm.UpdateLastLogin(System.Int32)">
	<summary>
 تحديث تاريخ آخر دخول للمستخدم
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.btnCancel_Click(System.Object,System.EventArgs)">
	<summary>
 معالج حدث النقر على زر الإلغاء
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.txtPassword_KeyPress(System.Object,System.Windows.Forms.KeyPressEventArgs)">
	<summary>
 معالج حدث الضغط على مفتاح في حقل كلمة المرور
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.txtUsername_KeyPress(System.Object,System.Windows.Forms.KeyPressEventArgs)">
	<summary>
 معالج حدث الضغط على مفتاح في حقل اسم المستخدم
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.txtUsername_Enter(System.Object,System.EventArgs)">
	<summary>
 معالج حدث دخول المؤشر لحقل اسم المستخدم
 </summary>
</member><member name="M:UnifiedAccountingSystem.LoginForm.txtPassword_Enter(System.Object,System.EventArgs)">
	<summary>
 معالج حدث دخول المؤشر لحقل كلمة المرور
 </summary>
</member><member name="T:UnifiedAccountingSystem.LoginForm">
	<summary>
 شاشة تسجيل الدخول للنظام المحاسبي الموحد
 </summary>
</member><member name="M:UnifiedAccountingSystem.CurrentUser.HasPermission(System.String)">
	<summary>
 التحقق من صلاحية المستخدم
 </summary>
</member><member name="M:UnifiedAccountingSystem.CurrentUser.Logout">
	<summary>
 تسجيل الخروج
 </summary>
</member><member name="T:UnifiedAccountingSystem.CurrentUser">
	<summary>
 فئة لحفظ بيانات المستخدم الحالي
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.GetAllEmployees">
	<summary>
 الحصول على جميع الموظفين
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.GetEmployeeById(System.Int32)">
	<summary>
 الحصول على موظف بالمعرف
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.SearchEmployees(System.String)">
	<summary>
 البحث عن الموظفين
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.AddEmployee(UnifiedAccountingSystem.Employee)">
	<summary>
 إضافة موظف جديد
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.UpdateEmployee(UnifiedAccountingSystem.Employee)">
	<summary>
 تحديث بيانات موظف
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.DeleteEmployee(System.Int32)">
	<summary>
 حذف موظف (حذف منطقي)
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.MapDataRowToEmployee(System.Data.DataRow)">
	<summary>
 تحويل صف البيانات إلى كائن موظف
 </summary>
</member><member name="M:UnifiedAccountingSystem.EmployeeDAL.CreateEmployeeParameters(UnifiedAccountingSystem.Employee)">
	<summary>
 إنشاء معاملات SQL للموظف
 </summary>
</member><member name="T:UnifiedAccountingSystem.EmployeeDAL">
	<summary>
 طبقة الوصول للبيانات - الموظفين
 </summary>
</member><member name="P:UnifiedAccountingSystem.DatabaseConnection.ConnectionString">
	<summary>
 الحصول على نص الاتصال بقاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.GetConnectionString">
	<summary>
 الحصول على نص الاتصال بقاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.CreateConnection">
	<summary>
 إنشاء اتصال جديد بقاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.TestConnection">
	<summary>
 اختبار الاتصال بقاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.ExecuteQuery(System.String,System.Data.SqlClient.SqlParameter[])">
	<summary>
 تنفيذ استعلام وإرجاع DataTable
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.ExecuteNonQuery(System.String,System.Data.SqlClient.SqlParameter[])">
	<summary>
 تنفيذ أمر غير استعلام (INSERT, UPDATE, DELETE)
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.ExecuteScalar(System.String,System.Data.SqlClient.SqlParameter[])">
	<summary>
 تنفيذ استعلام وإرجاع قيمة واحدة
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.CreateDatabaseIfNotExists">
	<summary>
 إنشاء قاعدة البيانات إذا لم تكن موجودة
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.ExecuteScript(System.String)">
	<summary>
 تنفيذ سكريبت SQL
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.BackupDatabase(System.String)">
	<summary>
 إنشاء نسخة احتياطية من قاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.DatabaseConnection.RestoreDatabase(System.String)">
	<summary>
 استعادة قاعدة البيانات من نسخة احتياطية
 </summary>
</member><member name="T:UnifiedAccountingSystem.DatabaseConnection">
	<summary>
 فئة إدارة الاتصال بقاعدة البيانات
 </summary>
</member><member name="M:UnifiedAccountingSystem.Module1.Main">
	<summary>
 نقطة البداية الرئيسية للتطبيق
 </summary>
</member>
</members>
</doc>