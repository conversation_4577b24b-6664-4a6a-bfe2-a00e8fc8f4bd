Imports System.Data.SqlClient
Imports System.Configuration

''' <summary>
''' شاشة تسجيل الدخول للنظام المحاسبي الموحد
''' </summary>
Public Class LoginForm
    
    Private Sub LoginForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة الشاشة
        InitializeForm()
        
        ' اختبار الاتصال بقاعدة البيانات
        TestDatabaseConnection()
    End Sub
    
    ''' <summary>
    ''' تهيئة عناصر الشاشة
    ''' </summary>
    Private Sub InitializeForm()
        ' تعيين خصائص النموذج
        Me.Text = "تسجيل الدخول - النظام المحاسبي الموحد"
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        
        ' تعيين الخط العربي
        Dim arabicFont As New Font("Cairo", 12, FontStyle.Regular)
        Me.Font = arabicFont
        
        ' تطبيق الخط على جميع العناصر
        ApplyFontToControls(Me, arabicFont)
        
        ' تعيين القيم الافتراضية
        txtUsername.Text = "admin"
        txtPassword.Text = ""
        txtUsername.Focus()
    End Sub
    
    ''' <summary>
    ''' تطبيق الخط على جميع العناصر
    ''' </summary>
    Private Sub ApplyFontToControls(parent As Control, font As Font)
        For Each ctrl As Control In parent.Controls
            ctrl.Font = font
            If ctrl.HasChildren Then
                ApplyFontToControls(ctrl, font)
            End If
        Next
    End Sub
    
    ''' <summary>
    ''' اختبار الاتصال بقاعدة البيانات
    ''' </summary>
    Private Sub TestDatabaseConnection()
        Try
            If Not DatabaseConnection.TestConnection() Then
                ' محاولة إنشاء قاعدة البيانات
                If MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. هل تريد إنشاء قاعدة بيانات جديدة؟", 
                                 "خطأ في الاتصال", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    
                    DatabaseConnection.CreateDatabaseIfNotExists()
                    MessageBox.Show("تم إنشاء قاعدة البيانات بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في إعداد قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ''' <summary>
    ''' معالج حدث النقر على زر تسجيل الدخول
    ''' </summary>
    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        If ValidateInput() Then
            PerformLogin()
        End If
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة البيانات المدخلة
    ''' </summary>
    Private Function ValidateInput() As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If
        
        Return True
    End Function
    
    ''' <summary>
    ''' تنفيذ عملية تسجيل الدخول
    ''' </summary>
    Private Sub PerformLogin()
        Try
            Dim username As String = txtUsername.Text.Trim()
            Dim password As String = txtPassword.Text
            
            ' التحقق من بيانات المستخدم
            Dim user As DataRow = AuthenticateUser(username, password)
            
            If user IsNot Nothing Then
                ' حفظ بيانات المستخدم الحالي
                CurrentUser.UserID = Convert.ToInt32(user("UserID"))
                CurrentUser.Username = user("Username").ToString()
                CurrentUser.FullName = user("FullName").ToString()
                CurrentUser.Role = user("Role").ToString()
                
                ' تحديث تاريخ آخر دخول
                UpdateLastLogin(CurrentUser.UserID)
                
                ' إخفاء شاشة تسجيل الدخول وفتح الشاشة الرئيسية
                Me.Hide()
                Dim mainForm As New MainForm()
                mainForm.Show()
                
            Else
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error)
                txtPassword.Clear()
                txtUsername.Focus()
            End If
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة بيانات المستخدم
    ''' </summary>
    Private Function AuthenticateUser(username As String, password As String) As DataRow
        Dim query As String = "SELECT * FROM Users WHERE Username = @Username AND Password = @Password AND IsActive = 1"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@Username", username),
            New SqlParameter("@Password", password)
        }
        
        Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
        
        If dataTable.Rows.Count > 0 Then
            Return dataTable.Rows(0)
        Else
            Return Nothing
        End If
    End Function
    
    ''' <summary>
    ''' تحديث تاريخ آخر دخول للمستخدم
    ''' </summary>
    Private Sub UpdateLastLogin(userId As Integer)
        Dim query As String = "UPDATE Users SET LastLogin = @LastLogin WHERE UserID = @UserID"
        
        Dim parameters() As SqlParameter = {
            New SqlParameter("@LastLogin", DateTime.Now),
            New SqlParameter("@UserID", userId)
        }
        
        DatabaseConnection.ExecuteNonQuery(query, parameters)
    End Sub
    
    ''' <summary>
    ''' معالج حدث النقر على زر الإلغاء
    ''' </summary>
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Application.Exit()
    End Sub
    
    ''' <summary>
    ''' معالج حدث الضغط على مفتاح في حقل كلمة المرور
    ''' </summary>
    Private Sub txtPassword_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtPassword.KeyPress
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            btnLogin_Click(sender, e)
        End If
    End Sub
    
    ''' <summary>
    ''' معالج حدث الضغط على مفتاح في حقل اسم المستخدم
    ''' </summary>
    Private Sub txtUsername_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtUsername.KeyPress
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            txtPassword.Focus()
        End If
    End Sub
End Class

''' <summary>
''' فئة لحفظ بيانات المستخدم الحالي
''' </summary>
Public Class CurrentUser
    Public Shared Property UserID As Integer
    Public Shared Property Username As String
    Public Shared Property FullName As String
    Public Shared Property Role As String
    
    ''' <summary>
    ''' التحقق من صلاحية المستخدم
    ''' </summary>
    Public Shared Function HasPermission(permission As String) As Boolean
        ' يمكن تطوير نظام صلاحيات أكثر تعقيداً هنا
        Select Case Role.ToLower()
            Case "مدير"
                Return True
            Case "محاسب"
                Return permission.Contains("محاسبة") OrElse permission.Contains("رواتب")
            Case "موظف"
                Return permission.Contains("عرض")
            Case Else
                Return False
        End Select
    End Function
    
    ''' <summary>
    ''' تسجيل الخروج
    ''' </summary>
    Public Shared Sub Logout()
        UserID = 0
        Username = ""
        FullName = ""
        Role = ""
    End Sub
End Class
