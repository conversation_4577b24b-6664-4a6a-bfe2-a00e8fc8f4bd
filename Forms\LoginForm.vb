Imports System.Data
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Windows.Forms
Imports DevExpress.XtraEditors

''' <summary>
''' شاشة تسجيل الدخول للنظام المحاسبي الموحد
''' </summary>
Public Class LoginForm
    
    Private Sub LoginForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة الشاشة
        InitializeForm()

        ' التأكد من توسيط الشاشة
        CenterFormOnScreen()

        ' التأكد من ظهور جميع العناصر
        EnsureControlsVisible()

        ' اختبار الاتصال بقاعدة البيانات
        TestDatabaseConnection()

        ' تطبيق التأثيرات البصرية
        AddVisualEffects()
    End Sub

    ''' <summary>
    ''' معالج حدث إظهار الشاشة
    ''' </summary>
    Private Sub LoginForm_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        ' التأكد من التوسيط مرة أخرى عند الإظهار
        CenterFormOnScreen()
        txtUsername.Focus()
        txtUsername.SelectAll()
    End Sub

    ''' <summary>
    ''' معالج حدث تفعيل الشاشة
    ''' </summary>
    Private Sub LoginForm_Activated(sender As Object, e As EventArgs) Handles MyBase.Activated
        ' التأكد من التوسيط عند تفعيل الشاشة
        If Me.WindowState = FormWindowState.Normal Then
            CenterFormOnScreen()
        End If
    End Sub

    ''' <summary>
    ''' تهيئة عناصر الشاشة
    ''' </summary>
    Private Sub InitializeForm()
        ' تعيين خصائص النموذج
        Me.Text = "تسجيل الدخول - النظام المحاسبي الموحد"
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = FormStartPosition.Manual
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False

        ' تعيين الخط العربي
        Dim arabicFont As Font = GetArabicFont()
        Me.Font = arabicFont

        ' تطبيق الخط على جميع العناصر
        ApplyFontToControls(Me, arabicFont)

        ' تعيين القيم الافتراضية
        txtUsername.Text = "admin"
        txtPassword.Text = ""
        txtUsername.Focus()

        ' إضافة تأثيرات بصرية
        AddVisualEffects()
    End Sub

    ''' <summary>
    ''' الحصول على الخط العربي المناسب
    ''' </summary>
    Private Function GetArabicFont() As Font
        Try
            ' محاولة استخدام خط Cairo
            Return New Font("Cairo", 12, FontStyle.Regular)
        Catch
            Try
                ' إذا لم يكن متوفراً، استخدم Tahoma
                Return New Font("Tahoma", 12, FontStyle.Regular)
            Catch
                ' إذا لم يكن متوفراً، استخدم الخط الافتراضي
                Return New Font("Microsoft Sans Serif", 12, FontStyle.Regular)
            End Try
        End Try
    End Function

    ''' <summary>
    ''' تطبيق الخط على جميع العناصر
    ''' </summary>
    Private Sub ApplyFontToControls(parent As Control, font As Font)
        For Each ctrl As Control In parent.Controls
            ctrl.Font = font
            If ctrl.HasChildren Then
                ApplyFontToControls(ctrl, font)
            End If
        Next
    End Sub

    ''' <summary>
    ''' توسيط الشاشة في وسط الشاشة
    ''' </summary>
    Private Sub CenterFormOnScreen()
        ' تحديد حجم الشاشة
        Me.Size = New Size(450, 320)

        ' الحصول على معلومات الشاشة الرئيسية
        Dim workingArea As Rectangle = Screen.PrimaryScreen.WorkingArea

        ' حساب الموضع المركزي
        Dim centerX As Integer = workingArea.Left + (workingArea.Width - Me.Width) \ 2
        Dim centerY As Integer = workingArea.Top + (workingArea.Height - Me.Height) \ 2

        ' تطبيق الموضع الجديد
        Me.Location = New Point(centerX, centerY)

        ' التأكد من أن الشاشة داخل حدود الشاشة
        If Me.Left < workingArea.Left Then Me.Left = workingArea.Left
        If Me.Top < workingArea.Top Then Me.Top = workingArea.Top
        If Me.Right > workingArea.Right Then Me.Left = workingArea.Right - Me.Width
        If Me.Bottom > workingArea.Bottom Then Me.Top = workingArea.Bottom - Me.Height

        ' التأكد من ظهور جميع العناصر
        EnsureControlsVisible()

        ' إجبار التحديث
        Me.Refresh()
    End Sub

    ''' <summary>
    ''' التأكد من ظهور جميع العناصر
    ''' </summary>
    Private Sub EnsureControlsVisible()
        ' التأكد من ظهور جميع العناصر
        For Each ctrl As Control In Me.Controls
            ctrl.Visible = True
            If ctrl.HasChildren Then
                MakeChildControlsVisible(ctrl)
            End If
        Next
    End Sub

    ''' <summary>
    ''' جعل العناصر الفرعية مرئية
    ''' </summary>
    Private Sub MakeChildControlsVisible(parent As Control)
        For Each ctrl As Control In parent.Controls
            ctrl.Visible = True
            If ctrl.HasChildren Then
                MakeChildControlsVisible(ctrl)
            End If
        Next
    End Sub

    ''' <summary>
    ''' إضافة تأثيرات بصرية للشاشة
    ''' </summary>
    Private Sub AddVisualEffects()
        ' إضافة حدود مستديرة للأزرار
        btnLogin.FlatStyle = FlatStyle.Flat
        btnLogin.FlatAppearance.BorderSize = 0
        btnCancel.FlatStyle = FlatStyle.Flat
        btnCancel.FlatAppearance.BorderSize = 0

        ' تحسين مظهر حقول النص
        txtUsername.BorderStyle = BorderStyle.FixedSingle
        txtPassword.BorderStyle = BorderStyle.FixedSingle

        ' إضافة ظلال للوحات
        pnlHeader.BackColor = Color.FromArgb(52, 152, 219)
        pnlLogin.BackColor = Color.FromArgb(248, 249, 250)

        ' تحسين الألوان
        Me.BackColor = Color.White

        ' إضافة تأثيرات hover للأزرار
        AddHandler btnLogin.MouseEnter, AddressOf Button_MouseEnter
        AddHandler btnLogin.MouseLeave, AddressOf Button_MouseLeave
        AddHandler btnCancel.MouseEnter, AddressOf Button_MouseEnter
        AddHandler btnCancel.MouseLeave, AddressOf Button_MouseLeave

        ' إضافة تأثيرات للحقول النصية
        AddHandler txtUsername.Enter, AddressOf TextBox_Enter
        AddHandler txtUsername.Leave, AddressOf TextBox_Leave
        AddHandler txtPassword.Enter, AddressOf TextBox_Enter
        AddHandler txtPassword.Leave, AddressOf TextBox_Leave

        ' تحسين مظهر الحقول النصية
        txtUsername.BackColor = Color.FromArgb(255, 255, 255)
        txtPassword.BackColor = Color.FromArgb(255, 255, 255)
    End Sub

    ''' <summary>
    ''' اختبار الاتصال بقاعدة البيانات
    ''' </summary>
    Private Sub TestDatabaseConnection()
        Try
            If Not DatabaseConnection.TestConnection() Then
                ' محاولة إنشاء قاعدة البيانات
                If MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. هل تريد إنشاء قاعدة بيانات جديدة؟",
                                 "خطأ في الاتصال", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then

                    DatabaseConnection.CreateDatabaseIfNotExists()
                    MessageBox.Show("تم إنشاء قاعدة البيانات بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في إعداد قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تأثير عند مرور الماوس على الزر
    ''' </summary>
    Private Sub Button_MouseEnter(sender As Object, e As EventArgs)
        Dim btn As Button = DirectCast(sender, Button)
        If btn Is btnLogin Then
            btn.BackColor = Color.FromArgb(41, 128, 185) ' لون أزرق أغمق
        ElseIf btn Is btnCancel Then
            btn.BackColor = Color.FromArgb(192, 57, 43) ' لون أحمر أغمق
        End If
    End Sub

    ''' <summary>
    ''' تأثير عند مغادرة الماوس للزر
    ''' </summary>
    Private Sub Button_MouseLeave(sender As Object, e As EventArgs)
        Dim btn As Button = DirectCast(sender, Button)
        If btn Is btnLogin Then
            btn.BackColor = Color.FromArgb(52, 152, 219) ' اللون الأصلي الأزرق
        ElseIf btn Is btnCancel Then
            btn.BackColor = Color.FromArgb(231, 76, 60) ' اللون الأصلي الأحمر
        End If
    End Sub

    ''' <summary>
    ''' تأثير عند دخول التركيز للحقل النصي
    ''' </summary>
    Private Sub TextBox_Enter(sender As Object, e As EventArgs)
        Dim txt As TextBox = DirectCast(sender, TextBox)
        txt.BackColor = Color.FromArgb(240, 248, 255) ' لون أزرق فاتح
    End Sub

    ''' <summary>
    ''' تأثير عند مغادرة التركيز للحقل النصي
    ''' </summary>
    Private Sub TextBox_Leave(sender As Object, e As EventArgs)
        Dim txt As TextBox = DirectCast(sender, TextBox)
        txt.BackColor = Color.FromArgb(255, 255, 255) ' اللون الأبيض الأصلي
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر تسجيل الدخول
    ''' </summary>
    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        If ValidateInput() Then
            PerformLogin()
        End If
    End Sub

    ''' <summary>
    ''' التحقق من صحة البيانات المدخلة
    ''' </summary>
    Private Function ValidateInput() As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' تنفيذ عملية تسجيل الدخول
    ''' </summary>
    Private Sub PerformLogin()
        Try
            ' التحقق من الاتصال بقاعدة البيانات
            If Not DatabaseConnection.TestConnection() Then
                MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.", "خطأ في الاتصال",
                              MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            Dim username As String = txtUsername.Text.Trim()
            Dim password As String = txtPassword.Text

            ' التحقق من بيانات المستخدم
            Dim user As DataRow = AuthenticateUser(username, password)

            If user IsNot Nothing Then
                ' حفظ بيانات المستخدم الحالي
                CurrentUser.UserID = Convert.ToInt32(user("UserID"))
                CurrentUser.Username = user("Username").ToString()
                CurrentUser.FullName = user("FullName").ToString()
                CurrentUser.Role = user("Role").ToString()

                ' إخفاء شاشة تسجيل الدخول وفتح الشاشة الرئيسية
                Me.Hide()
                Dim mainForm As New MainForm()
                mainForm.Show()

            Else
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                              MessageBoxButtons.OK, MessageBoxIcon.Error)
                txtPassword.Clear()
                txtUsername.Focus()
            End If

        Catch ex As Exception
            MessageBox.Show("خطأ في تسجيل الدخول: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق من صحة بيانات المستخدم
    ''' </summary>
    ''' <param name="username">اسم المستخدم</param>
    ''' <param name="password">كلمة المرور</param>
    ''' <returns>بيانات المستخدم إذا كانت صحيحة، Nothing إذا كانت خاطئة</returns>
    Private Function AuthenticateUser(username As String, password As String) As DataRow
        Try
            Dim query As String = "SELECT UserID, Username, FullName, Role FROM Users WHERE Username = @Username AND Password = @Password AND IsActive = 1"

            Using connection As New SqlConnection(DatabaseConnection.GetConnectionString())
                Using command As New SqlCommand(query, connection)
                    command.Parameters.AddWithValue("@Username", username)
                    command.Parameters.AddWithValue("@Password", password) ' في التطبيق الحقيقي يجب تشفير كلمة المرور

                    connection.Open()
                    Using adapter As New SqlDataAdapter(command)
                        Dim table As New DataTable()
                        adapter.Fill(table)

                        If table.Rows.Count > 0 Then
                            Return table.Rows(0)
                        Else
                            Return Nothing
                        End If
                    End Using
                End Using
            End Using

        Catch ex As Exception
            Throw New Exception("خطأ في التحقق من بيانات المستخدم: " & ex.Message)
        End Try
    End Function


    ''' <summary>
    ''' تحديث تاريخ آخر دخول للمستخدم
    ''' </summary>
    Private Sub UpdateLastLogin(userId As Integer)
        ' تم إزالة هذه الوظيفة مؤقتاً لتبسيط النظام
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر الإلغاء
    ''' </summary>
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Application.Exit()
    End Sub

    ''' <summary>
    ''' معالج حدث الضغط على مفتاح في حقل كلمة المرور
    ''' </summary>
    Private Sub txtPassword_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtPassword.KeyPress
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            btnLogin_Click(sender, e)
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث الضغط على مفتاح في حقل اسم المستخدم
    ''' </summary>
    Private Sub txtUsername_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtUsername.KeyPress
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            e.Handled = True
            txtPassword.Focus()
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث دخول المؤشر لحقل اسم المستخدم
    ''' </summary>
    Private Sub txtUsername_Enter(sender As Object, e As EventArgs) Handles txtUsername.Enter
        txtUsername.SelectAll()
    End Sub

    ''' <summary>
    ''' معالج حدث دخول المؤشر لحقل كلمة المرور
    ''' </summary>
    Private Sub txtPassword_Enter(sender As Object, e As EventArgs) Handles txtPassword.Enter
        txtPassword.SelectAll()
    End Sub
End Class

''' <summary>
''' فئة لحفظ بيانات المستخدم الحالي
''' </summary>
Public Class CurrentUser
    Public Shared Property UserID As Integer
    Public Shared Property Username As String
    Public Shared Property FullName As String
    Public Shared Property Role As String
    
    ''' <summary>
    ''' التحقق من صلاحية المستخدم
    ''' </summary>
    Public Shared Function HasPermission(permission As String) As Boolean
        ' يمكن تطوير نظام صلاحيات أكثر تعقيداً هنا
        Select Case Role.ToLower()
            Case "مدير"
                Return True
            Case "محاسب"
                Return permission.Contains("محاسبة") OrElse permission.Contains("رواتب")
            Case "موظف"
                Return permission.Contains("عرض")
            Case Else
                Return False
        End Select
    End Function
    
    ''' <summary>
    ''' تسجيل الخروج
    ''' </summary>
    Public Shared Sub Logout()
        UserID = 0
        Username = ""
        FullName = ""
        Role = ""
    End Sub
End Class
