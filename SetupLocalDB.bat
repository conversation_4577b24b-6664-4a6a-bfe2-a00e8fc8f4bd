@echo off
chcp 65001 >nul
echo ========================================
echo إعداد قاعدة البيانات المحلية
echo النظام المحاسبي الموحد
echo وزارة الشباب والرياضة
echo ========================================
echo.

echo جاري إعداد قاعدة البيانات المحلية...
echo.

REM التحقق من وجود LocalDB
echo [1/3] التحقق من LocalDB...
sqllocaldb info MSSQLLocalDB >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: LocalDB غير متوفر، جاري إنشاء instance جديد...
    sqllocaldb create MSSQLLocalDB
    sqllocaldb start MSSQLLocalDB
) else (
    echo ✓ LocalDB متوفر
    sqllocaldb start MSSQLLocalDB >nul 2>&1
)

REM إنشاء مجلد البيانات
echo [2/3] إنشاء مجلد البيانات...
if not exist "App_Data" mkdir App_Data
echo ✓ مجلد البيانات جاهز

REM إنشاء قاعدة البيانات
echo [3/3] إنشاء قاعدة البيانات...
sqlcmd -S "(LocalDB)\MSSQLLocalDB" -i "Database\CreateLocalDB.sql" -o setup_log.txt
if %errorlevel% neq 0 (
    echo ❌ خطأ في إنشاء قاعدة البيانات
    echo راجع ملف setup_log.txt للتفاصيل
    pause
    exit /b 1
) else (
    echo ✓ تم إنشاء قاعدة البيانات بنجاح
)

echo.
echo ========================================
echo ✅ تم إعداد قاعدة البيانات بنجاح!
echo.
echo يمكنك الآن تشغيل النظام باستخدام:
echo - RunSystem.bat
echo - أو F5 في Visual Studio
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo ========================================
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
