@echo off
chcp 65001 >nul
echo ========================================================
echo        🎯 اختبار DevExpress v20.1 مع Visual Studio 2013
echo ========================================================
echo.

cd /d "%~dp0"

echo 📋 التحديثات المطبقة للإصدار 20.1:
echo ✅ تحديث جميع المراجع إلى v20.1
echo ✅ تحديث مسارات البحث للإصدار 20.1
echo ✅ تحديث الثيمات المتوافقة مع v20.1
echo ✅ تحديث شاشة DevExpress للتوافق
echo ✅ دعم Visual Studio 2013
echo.

echo 🔧 المراجع المحدثة:
echo    • DevExpress.Utils.v20.1.dll
echo    • DevExpress.Data.v20.1.dll
echo    • DevExpress.XtraEditors.v20.1.dll
echo    • DevExpress.XtraGrid.v20.1.dll
echo    • DevExpress.XtraLayout.v20.1.dll
echo    • DevExpress.XtraBars.v20.1.dll
echo    • DevExpress.XtraNavBar.v20.1.dll
echo    • DevExpress.XtraReports.v20.1.dll
echo.

echo 📂 مسارات البحث المحدثة:
echo    • C:\Program Files (x86)\DevExpress 20.1\Components\Bin\Framework
echo    • C:\Program Files\DevExpress 20.1\Components\Bin\Framework
echo    • C:\Program Files (x86)\DevExpress 20.2\Components\Bin\Framework
echo    • C:\Program Files\DevExpress 20.2\Components\Bin\Framework
echo.

echo 🎨 الثيمات المتوافقة مع v20.1:
echo    • Office 2016 Colorful (بدلاً من Office 2019)
echo    • Office 2016 Dark
echo    • Office 2013 White
echo    • Visual Studio 2013 Blue
echo    • DevExpress Style
echo.

echo 🔍 فحص تثبيت DevExpress v20.1...
echo.

set DEVEXPRESS_FOUND=0
set DEVEXPRESS_PATH=""

if exist "C:\Program Files (x86)\DevExpress 20.1\Components\Bin\Framework\DevExpress.Utils.v20.1.dll" (
    echo ✅ تم العثور على DevExpress 20.1 في: C:\Program Files (x86)\DevExpress 20.1
    set DEVEXPRESS_FOUND=1
    set DEVEXPRESS_PATH=C:\Program Files (x86)\DevExpress 20.1\Components\Bin\Framework
) else if exist "C:\Program Files\DevExpress 20.1\Components\Bin\Framework\DevExpress.Utils.v20.1.dll" (
    echo ✅ تم العثور على DevExpress 20.1 في: C:\Program Files\DevExpress 20.1
    set DEVEXPRESS_FOUND=1
    set DEVEXPRESS_PATH=C:\Program Files\DevExpress 20.1\Components\Bin\Framework
) else (
    echo ❌ لم يتم العثور على DevExpress 20.1
)

if %DEVEXPRESS_FOUND% equ 1 (
    echo.
    echo 🎉 تم العثور على DevExpress 20.1!
    echo 📍 المسار: %DEVEXPRESS_PATH%
    echo.
    
    echo 🔍 فحص المكتبات الأساسية:
    if exist "%DEVEXPRESS_PATH%\DevExpress.Utils.v20.1.dll" (
        echo ✅ DevExpress.Utils.v20.1.dll
    ) else (
        echo ❌ DevExpress.Utils.v20.1.dll غير موجود
    )
    
    if exist "%DEVEXPRESS_PATH%\DevExpress.XtraEditors.v20.1.dll" (
        echo ✅ DevExpress.XtraEditors.v20.1.dll
    ) else (
        echo ❌ DevExpress.XtraEditors.v20.1.dll غير موجود
    )
    
    if exist "%DEVEXPRESS_PATH%\DevExpress.XtraGrid.v20.1.dll" (
        echo ✅ DevExpress.XtraGrid.v20.1.dll
    ) else (
        echo ❌ DevExpress.XtraGrid.v20.1.dll غير موجود
    )
    
    echo.
    echo 🔧 بناء المشروع مع DevExpress v20.1...
    
    if exist "C:\Program Files (x86)\Microsoft Visual Studio 12.0\Common7\IDE\devenv.exe" (
        echo استخدام Visual Studio 2013...
        "C:\Program Files (x86)\Microsoft Visual Studio 12.0\Common7\Tools\VsMSBuildCmd.bat"
        msbuild UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        echo استخدام Visual Studio 2022...
        "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    ) else (
        echo محاولة استخدام MSBuild العام...
        msbuild UnifiedAccountingSystem.sln /p:Configuration=Debug /verbosity:minimal
    )
    
    if %errorlevel% equ 0 (
        echo ✅ تم بناء المشروع بنجاح مع DevExpress v20.1!
        echo.
        echo 🎯 المميزات المتاحة الآن:
        echo    • شاشات XtraForm محسنة
        echo    • عناصر TextEdit متقدمة
        echo    • أزرار SimpleButton أنيقة
        echo    • جداول GridControl قوية
        echo    • تخطيط LayoutControl مرن
        echo    • قوائم BarManager متطورة
        echo    • رسائل XtraMessageBox جميلة
        echo.
        
        echo 💡 للاستخدام في Visual Studio 2013:
        echo    1. افتح المشروع في VS 2013
        echo    2. تأكد من ظهور مراجع DevExpress في References
        echo    3. استخدم DevExpressLoginForm بدلاً من LoginForm
        echo    4. جرب إنشاء شاشات جديدة بعناصر DevExpress
        echo.
        
    ) else (
        echo ❌ فشل في بناء المشروع!
        echo 💡 تأكد من:
        echo    • تثبيت DevExpress v20.1 بشكل صحيح
        echo    • تفعيل الترخيص
        echo    • توافق الإصدار مع Visual Studio 2013
    )
    
) else (
    echo.
    echo ❌ لم يتم العثور على DevExpress v20.1!
    echo.
    echo 💡 للحصول على DevExpress v20.1:
    echo    1. قم بتحميل الإصدار من الموقع الرسمي
    echo    2. تأكد من اختيار الإصدار 20.1 المتوافق مع VS 2013
    echo    3. قم بالتثبيت مع تفعيل الترخيص
    echo    4. أعد تشغيل Visual Studio
    echo.
    echo 🔗 رابط DevExpress:
    echo    https://www.devexpress.com/products/net/controls/winforms/
    echo.
)

echo 📝 ملاحظات مهمة للإصدار 20.1:
echo    • متوافق تماماً مع Visual Studio 2013
echo    • يدعم .NET Framework 4.5.2
echo    • ثيمات Office 2016 بدلاً من 2019
echo    • جميع المراجع محدثة للإصدار الصحيح
echo    • يمكن التبديل بين الشاشة العادية وشاشة DevExpress
echo.

echo 🎮 للاختبار:
echo    • افتح المشروع في Visual Studio 2013
echo    • شغل المشروع (F5)
echo    • جرب استخدام DevExpressLoginForm
echo    • تحقق من ظهور الثيمات الجديدة
echo.

pause
